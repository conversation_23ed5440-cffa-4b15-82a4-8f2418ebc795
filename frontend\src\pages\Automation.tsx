import React from 'react';
import { PlusIcon, PlayIcon, PauseIcon, TrashIcon } from '@heroicons/react/24/outline';

const tasks = [
  {
    id: 1,
    name: 'Email Organization',
    description: 'Organize emails by sender and date',
    status: 'active',
    schedule: 'Daily at 9:00 AM',
    lastRun: '2025-09-14T09:00:00Z',
    nextRun: '2025-09-15T09:00:00Z',
  },
  {
    id: 2,
    name: 'System Cleanup',
    description: 'Clean temporary files and logs',
    status: 'active',
    schedule: 'Weekly on Sunday at 2:00 AM',
    lastRun: '2025-09-08T02:00:00Z',
    nextRun: '2025-09-15T02:00:00Z',
  },
  {
    id: 3,
    name: 'Backup Documents',
    description: 'Backup important documents to cloud',
    status: 'paused',
    schedule: 'Daily at 11:00 PM',
    lastRun: '2025-09-13T23:00:00Z',
    nextRun: null,
  },
];

export default function Automation() {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Automation Tasks</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage your automated tasks and schedules
          </p>
        </div>
        <button className="btn-primary px-4 py-2 flex items-center space-x-2">
          <PlusIcon className="h-5 w-5" />
          <span>Create Task</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Tasks</dt>
                  <dd className="text-lg font-medium text-gray-900">2</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Paused Tasks</dt>
                  <dd className="text-lg font-medium text-gray-900">1</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full"></div>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Executions</dt>
                  <dd className="text-lg font-medium text-gray-900">847</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks List */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {tasks.map((task) => (
            <li key={task.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div
                        className={`w-3 h-3 rounded-full ${
                          task.status === 'active' ? 'bg-green-500' : 'bg-yellow-500'
                        }`}
                      ></div>
                    </div>
                    <div className="ml-4">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-primary-600 truncate">
                          {task.name}
                        </p>
                        <span
                          className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            task.status === 'active'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {task.status}
                        </span>
                      </div>
                      <p className="mt-1 text-sm text-gray-500">{task.description}</p>
                      <div className="mt-2 flex items-center text-sm text-gray-500">
                        <p>Schedule: {task.schedule}</p>
                        <span className="mx-2">•</span>
                        <p>Last run: {formatDate(task.lastRun)}</p>
                        {task.nextRun && (
                          <>
                            <span className="mx-2">•</span>
                            <p>Next run: {formatDate(task.nextRun)}</p>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-primary-600 hover:text-primary-900">
                      <PlayIcon className="h-5 w-5" />
                    </button>
                    <button className="text-yellow-600 hover:text-yellow-900">
                      <PauseIcon className="h-5 w-5" />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
