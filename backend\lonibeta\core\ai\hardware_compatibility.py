"""
Hardware compatibility analysis for AI models.
"""

import math
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from .model_discovery import ModelInfo, QuantizationType
from ..monitoring.system_monitor import HardwareSpecs, GPUInfo, ComputeCapability


class CompatibilityRating(Enum):
    """Hardware compatibility ratings."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"
    INCOMPATIBLE = "incompatible"


class PerformanceLevel(Enum):
    """Performance level indicators."""
    VERY_FAST = "very_fast"
    FAST = "fast"
    MODERATE = "moderate"
    SLOW = "slow"
    VERY_SLOW = "very_slow"


@dataclass
class MemoryRequirement:
    """Memory requirements for different configurations."""
    minimum_ram_gb: float
    recommended_ram_gb: float
    minimum_vram_gb: float
    recommended_vram_gb: float
    quantization: QuantizationType


@dataclass
class PerformanceEstimate:
    """Performance estimation for a model on specific hardware."""
    tokens_per_second: float
    inference_latency_ms: float
    memory_usage_gb: float
    gpu_utilization_percent: float
    performance_level: PerformanceLevel
    bottleneck: str  # "cpu", "memory", "gpu", "none"


@dataclass
class CompatibilityAnalysis:
    """Complete compatibility analysis for a model."""
    model_id: str
    compatibility_rating: CompatibilityRating
    can_run: bool
    memory_requirements: Dict[QuantizationType, MemoryRequirement]
    performance_estimates: Dict[QuantizationType, PerformanceEstimate]
    recommendations: List[str]
    warnings: List[str]
    alternative_models: List[str]
    optimal_quantization: QuantizationType
    estimated_download_time_minutes: float


class HardwareCompatibilityEngine:
    """Engine for analyzing hardware compatibility with AI models."""

    def __init__(self):
        # Base memory multipliers for different model types
        self.memory_multipliers = {
            "text_generation": 1.2,  # Extra memory for context and generation
            "embeddings": 0.8,       # Less memory needed
            "vision": 1.5,           # More memory for image processing
            "code": 1.3,             # Code models need extra context
            "multimodal": 2.0,       # Highest memory requirements
            "audio": 1.1             # Moderate memory needs
        }

        # Quantization memory reduction factors
        self.quantization_factors = {
            QuantizationType.FP32: 1.0,
            QuantizationType.FP16: 0.5,
            QuantizationType.INT8: 0.25,
            QuantizationType.INT4: 0.125
        }

        # Performance impact of quantization (relative to FP32)
        self.quantization_performance = {
            QuantizationType.FP32: 1.0,
            QuantizationType.FP16: 1.2,
            QuantizationType.INT8: 1.5,
            QuantizationType.INT4: 2.0
        }

    async def analyze_compatibility(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        internet_speed_mbps: float = 100.0
    ) -> CompatibilityAnalysis:
        """Analyze compatibility between a model and hardware."""
        
        # Calculate memory requirements for each quantization
        memory_requirements = self._calculate_memory_requirements(model)
        
        # Estimate performance for each quantization
        performance_estimates = self._estimate_performance(model, hardware, memory_requirements)
        
        # Determine overall compatibility
        compatibility_rating, can_run = self._determine_compatibility(
            model, hardware, memory_requirements, performance_estimates
        )
        
        # Generate recommendations and warnings
        recommendations = self._generate_recommendations(model, hardware, performance_estimates)
        warnings = self._generate_warnings(model, hardware, memory_requirements)
        
        # Find alternative models if needed
        alternative_models = self._suggest_alternatives(model, hardware, compatibility_rating)
        
        # Find optimal quantization
        optimal_quantization = self._find_optimal_quantization(
            hardware, memory_requirements, performance_estimates
        )
        
        # Estimate download time
        download_time = self._estimate_download_time(model, internet_speed_mbps)
        
        return CompatibilityAnalysis(
            model_id=model.id,
            compatibility_rating=compatibility_rating,
            can_run=can_run,
            memory_requirements=memory_requirements,
            performance_estimates=performance_estimates,
            recommendations=recommendations,
            warnings=warnings,
            alternative_models=alternative_models,
            optimal_quantization=optimal_quantization,
            estimated_download_time_minutes=download_time
        )

    def _calculate_memory_requirements(self, model: ModelInfo) -> Dict[QuantizationType, MemoryRequirement]:
        """Calculate memory requirements for different quantizations."""
        requirements = {}
        
        # Base memory calculation
        base_memory_gb = model.size_gb
        model_type_key = model.model_type.value
        multiplier = self.memory_multipliers.get(model_type_key, 1.0)
        
        for quantization in model.available_quantizations:
            factor = self.quantization_factors[quantization]
            
            # Model memory with quantization
            model_memory = base_memory_gb * factor
            
            # Additional memory for inference (context, activations, etc.)
            inference_overhead = model_memory * multiplier * 0.5
            
            # Total memory needed
            total_memory = model_memory + inference_overhead
            
            # VRAM requirements (prefer GPU if available)
            vram_needed = model_memory * 1.1  # 10% overhead for GPU operations
            
            requirements[quantization] = MemoryRequirement(
                minimum_ram_gb=total_memory * 0.8,  # Can run with less RAM
                recommended_ram_gb=total_memory * 1.2,  # Recommended for good performance
                minimum_vram_gb=vram_needed * 0.9,
                recommended_vram_gb=vram_needed * 1.1,
                quantization=quantization
            )
        
        return requirements

    def _estimate_performance(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        memory_requirements: Dict[QuantizationType, MemoryRequirement]
    ) -> Dict[QuantizationType, PerformanceEstimate]:
        """Estimate performance for different quantizations."""
        estimates = {}
        
        # Base performance factors
        cpu_factor = self._calculate_cpu_performance_factor(hardware)
        gpu_factor = self._calculate_gpu_performance_factor(hardware)
        
        for quantization, mem_req in memory_requirements.items():
            # Determine if we can use GPU
            can_use_gpu = self._can_use_gpu(hardware, mem_req)
            
            # Calculate tokens per second
            if can_use_gpu:
                # GPU performance
                base_tokens_per_sec = gpu_factor * 50  # Base GPU performance
                memory_usage = mem_req.minimum_vram_gb
                gpu_utilization = min(95.0, (mem_req.minimum_vram_gb / self._get_total_vram(hardware)) * 100)
            else:
                # CPU performance
                base_tokens_per_sec = cpu_factor * 10  # Base CPU performance
                memory_usage = mem_req.minimum_ram_gb
                gpu_utilization = 0.0
            
            # Apply quantization performance boost
            quant_boost = self.quantization_performance[quantization]
            tokens_per_sec = base_tokens_per_sec * quant_boost
            
            # Calculate latency (time for first token)
            latency_ms = 1000 / max(tokens_per_sec, 0.1)  # Avoid division by zero
            
            # Determine performance level
            performance_level = self._classify_performance(tokens_per_sec)
            
            # Identify bottleneck
            bottleneck = self._identify_bottleneck(hardware, mem_req, can_use_gpu)
            
            estimates[quantization] = PerformanceEstimate(
                tokens_per_second=tokens_per_sec,
                inference_latency_ms=latency_ms,
                memory_usage_gb=memory_usage,
                gpu_utilization_percent=gpu_utilization,
                performance_level=performance_level,
                bottleneck=bottleneck
            )
        
        return estimates

    def _calculate_cpu_performance_factor(self, hardware: HardwareSpecs) -> float:
        """Calculate CPU performance factor."""
        # Base factor from core count and frequency
        core_factor = math.log2(hardware.cpu_cores + 1)
        freq_factor = hardware.cpu_frequency_mhz / 3000.0  # Normalize to 3GHz
        
        # Architecture bonus
        arch_bonus = 1.0
        if "arm" in hardware.cpu_architecture.lower():
            arch_bonus = 1.2  # ARM efficiency bonus
        elif "x86_64" in hardware.cpu_architecture.lower():
            arch_bonus = 1.1
        
        return core_factor * freq_factor * arch_bonus

    def _calculate_gpu_performance_factor(self, hardware: HardwareSpecs) -> float:
        """Calculate GPU performance factor."""
        if not hardware.gpus:
            return 0.0
        
        # Use the best GPU
        best_gpu = max(hardware.gpus, key=lambda g: g.memory_total_mb)
        
        # Base factor from memory and type
        memory_factor = best_gpu.memory_total_mb / 8192.0  # Normalize to 8GB
        
        type_multiplier = {
            "nvidia": 2.0,  # CUDA optimization
            "amd": 1.5,     # OpenCL support
            "intel": 1.2,   # Basic acceleration
            "apple": 1.8,   # Metal optimization
            "unknown": 1.0
        }
        
        gpu_multiplier = type_multiplier.get(best_gpu.gpu_type.value, 1.0)
        
        return memory_factor * gpu_multiplier

    def _can_use_gpu(self, hardware: HardwareSpecs, mem_req: MemoryRequirement) -> bool:
        """Check if GPU can be used for this model."""
        if not hardware.gpus:
            return False
        
        # Check if any GPU has enough memory
        for gpu in hardware.gpus:
            gpu_memory_gb = gpu.memory_total_mb / 1024.0
            if gpu_memory_gb >= mem_req.minimum_vram_gb:
                return True
        
        return False

    def _get_total_vram(self, hardware: HardwareSpecs) -> float:
        """Get total VRAM across all GPUs."""
        return sum(gpu.memory_total_mb / 1024.0 for gpu in hardware.gpus)

    def _classify_performance(self, tokens_per_sec: float) -> PerformanceLevel:
        """Classify performance level based on tokens per second."""
        if tokens_per_sec >= 100:
            return PerformanceLevel.VERY_FAST
        elif tokens_per_sec >= 50:
            return PerformanceLevel.FAST
        elif tokens_per_sec >= 20:
            return PerformanceLevel.MODERATE
        elif tokens_per_sec >= 5:
            return PerformanceLevel.SLOW
        else:
            return PerformanceLevel.VERY_SLOW

    def _identify_bottleneck(
        self,
        hardware: HardwareSpecs,
        mem_req: MemoryRequirement,
        can_use_gpu: bool
    ) -> str:
        """Identify the primary performance bottleneck."""
        # Check memory constraints
        if hardware.memory_available_gb < mem_req.minimum_ram_gb:
            return "memory"
        
        # Check GPU constraints
        if can_use_gpu:
            total_vram = self._get_total_vram(hardware)
            if total_vram < mem_req.recommended_vram_gb:
                return "gpu"
        
        # Check CPU
        if hardware.cpu_cores < 4 or hardware.cpu_frequency_mhz < 2000:
            return "cpu"
        
        return "none"

    def _determine_compatibility(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        memory_requirements: Dict[QuantizationType, MemoryRequirement],
        performance_estimates: Dict[QuantizationType, PerformanceEstimate]
    ) -> Tuple[CompatibilityRating, bool]:
        """Determine overall compatibility rating."""
        
        # Check if any quantization can run
        can_run = False
        best_performance = PerformanceLevel.VERY_SLOW
        
        for quant, perf in performance_estimates.items():
            mem_req = memory_requirements[quant]
            
            # Check if this quantization can run
            if (hardware.memory_available_gb >= mem_req.minimum_ram_gb or
                self._can_use_gpu(hardware, mem_req)):
                can_run = True
                if perf.performance_level.value < best_performance.value:
                    best_performance = perf.performance_level
        
        if not can_run:
            return CompatibilityRating.INCOMPATIBLE, False
        
        # Rate based on best achievable performance
        if best_performance == PerformanceLevel.VERY_FAST:
            return CompatibilityRating.EXCELLENT, True
        elif best_performance == PerformanceLevel.FAST:
            return CompatibilityRating.GOOD, True
        elif best_performance == PerformanceLevel.MODERATE:
            return CompatibilityRating.FAIR, True
        else:
            return CompatibilityRating.POOR, True

    def _generate_recommendations(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        performance_estimates: Dict[QuantizationType, PerformanceEstimate]
    ) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        # GPU recommendations
        if not hardware.gpus:
            recommendations.append("Consider adding a GPU for significantly better performance")
        elif hardware.gpus:
            total_vram = self._get_total_vram(hardware)
            if total_vram < 8:
                recommendations.append("Upgrade to a GPU with at least 8GB VRAM for better performance")
        
        # Memory recommendations
        if hardware.memory_total_gb < 16:
            recommendations.append("Upgrade to at least 16GB RAM for optimal performance")
        
        # Quantization recommendations
        best_quant = None
        best_perf = 0
        for quant, perf in performance_estimates.items():
            if perf.tokens_per_second > best_perf:
                best_perf = perf.tokens_per_second
                best_quant = quant
        
        if best_quant and best_quant != QuantizationType.FP32:
            recommendations.append(f"Use {best_quant.value} quantization for optimal performance")
        
        return recommendations

    def _generate_warnings(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        memory_requirements: Dict[QuantizationType, MemoryRequirement]
    ) -> List[str]:
        """Generate compatibility warnings."""
        warnings = []
        
        # Memory warnings
        min_memory_needed = min(req.minimum_ram_gb for req in memory_requirements.values())
        if hardware.memory_available_gb < min_memory_needed:
            warnings.append(f"Insufficient RAM: {min_memory_needed:.1f}GB required, {hardware.memory_available_gb:.1f}GB available")
        
        # GPU warnings
        if hardware.gpus:
            min_vram_needed = min(req.minimum_vram_gb for req in memory_requirements.values())
            total_vram = self._get_total_vram(hardware)
            if total_vram < min_vram_needed:
                warnings.append(f"Insufficient VRAM: {min_vram_needed:.1f}GB required, {total_vram:.1f}GB available")
        
        # Performance warnings
        if hardware.cpu_cores < 4:
            warnings.append("CPU has fewer than 4 cores, performance may be limited")
        
        return warnings

    def _suggest_alternatives(
        self,
        model: ModelInfo,
        hardware: HardwareSpecs,
        compatibility: CompatibilityRating
    ) -> List[str]:
        """Suggest alternative models if compatibility is poor."""
        alternatives = []
        
        if compatibility in [CompatibilityRating.POOR, CompatibilityRating.INCOMPATIBLE]:
            # Suggest smaller models of the same type
            if "7b" in model.name.lower():
                alternatives.append("Consider a smaller 3B parameter model")
            elif "13b" in model.name.lower():
                alternatives.append("Consider a 7B parameter model instead")
            elif "large" in model.name.lower():
                alternatives.append("Consider the 'base' or 'small' variant")
            
            # Suggest quantized versions
            if QuantizationType.INT4 not in model.available_quantizations:
                alternatives.append("Look for 4-bit quantized versions of this model")
        
        return alternatives

    def _find_optimal_quantization(
        self,
        hardware: HardwareSpecs,
        memory_requirements: Dict[QuantizationType, MemoryRequirement],
        performance_estimates: Dict[QuantizationType, PerformanceEstimate]
    ) -> QuantizationType:
        """Find the optimal quantization for the hardware."""
        best_quant = QuantizationType.FP16
        best_score = 0
        
        for quant, perf in performance_estimates.items():
            mem_req = memory_requirements[quant]
            
            # Check if this quantization can run
            can_run = (hardware.memory_available_gb >= mem_req.minimum_ram_gb or
                      self._can_use_gpu(hardware, mem_req))
            
            if can_run:
                # Score based on performance and memory efficiency
                score = perf.tokens_per_second * (1 / mem_req.minimum_ram_gb)
                if score > best_score:
                    best_score = score
                    best_quant = quant
        
        return best_quant

    def _estimate_download_time(self, model: ModelInfo, internet_speed_mbps: float) -> float:
        """Estimate download time in minutes."""
        size_mb = model.size_gb * 1024
        download_speed_mbps = internet_speed_mbps * 0.8  # Account for overhead
        time_seconds = (size_mb * 8) / download_speed_mbps  # Convert to bits
        return time_seconds / 60  # Convert to minutes
