import React, { useState } from 'react';
import { PaperAirplaneIcon, MicrophoneIcon, DownloadIcon } from '@heroicons/react/24/outline';

const models = [
  {
    id: 'llama2-7b',
    name: 'Llama 2 7B',
    provider: 'Ollama',
    type: 'Chat',
    status: 'available',
    size: '3.8GB',
  },
  {
    id: 'mistral-7b',
    name: 'Mistral 7B',
    provider: 'Ollama',
    type: 'Chat',
    status: 'available',
    size: '4.1GB',
  },
  {
    id: 'sentence-transformers/all-MiniLM-L6-v2',
    name: 'All MiniLM L6 v2',
    provider: 'HuggingFace',
    type: 'Embedding',
    status: 'available',
    size: '90MB',
  },
];

const chatHistory = [
  {
    id: 1,
    type: 'user',
    message: 'Hello! Can you help me organize my files?',
    timestamp: '2025-09-14T23:10:00Z',
  },
  {
    id: 2,
    type: 'assistant',
    message: 'Hello! I\'d be happy to help you organize your files. I can help you create automation tasks to sort files by type, date, or other criteria. What specific organization would you like to implement?',
    timestamp: '2025-09-14T23:10:05Z',
  },
];

export default function AI() {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);

  const handleSendMessage = () => {
    if (message.trim()) {
      // TODO: Implement message sending
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    // TODO: Implement voice recording
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">AI & Machine Learning</h1>
        <p className="mt-1 text-sm text-gray-500">
          Interact with AI models and manage machine learning capabilities
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg h-96 flex flex-col">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">AI Chat</h3>
            </div>
            
            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className={`flex ${chat.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      chat.type === 'user'
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <p className="text-sm">{chat.message}</p>
                    <p className={`text-xs mt-1 ${
                      chat.type === 'user' ? 'text-primary-100' : 'text-gray-500'
                    }`}>
                      {new Date(chat.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type your message..."
                  className="input flex-1"
                />
                <button
                  onClick={handleVoiceRecord}
                  className={`p-2 rounded-md ${
                    isRecording
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <MicrophoneIcon className="h-5 w-5" />
                </button>
                <button
                  onClick={handleSendMessage}
                  className="btn-primary p-2"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Models Panel */}
        <div className="space-y-6">
          {/* Available Models */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Available Models</h3>
            </div>
            <div className="p-4 space-y-3">
              {models.map((model) => (
                <div key={model.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{model.name}</p>
                      <p className="text-xs text-gray-500">{model.provider} • {model.type}</p>
                      <p className="text-xs text-gray-500">{model.size}</p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        model.status === 'available'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {model.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Download New Model */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Download Model</h3>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Model ID</label>
                <input
                  type="text"
                  placeholder="e.g., llama2:13b"
                  className="input mt-1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Provider</label>
                <select className="input mt-1">
                  <option value="ollama">Ollama</option>
                  <option value="huggingface">HuggingFace</option>
                  <option value="github">GitHub</option>
                </select>
              </div>
              <button className="btn-primary w-full flex items-center justify-center space-x-2">
                <DownloadIcon className="h-4 w-4" />
                <span>Download Model</span>
              </button>
            </div>
          </div>

          {/* Voice Settings */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Voice Settings</h3>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Language</label>
                <select className="input mt-1">
                  <option value="en-US">English (US)</option>
                  <option value="en-GB">English (UK)</option>
                  <option value="es-ES">Spanish</option>
                  <option value="fr-FR">French</option>
                  <option value="de-DE">German</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Sample Rate</label>
                <select className="input mt-1">
                  <option value="16000">16 kHz</option>
                  <option value="22050">22.05 kHz</option>
                  <option value="44100">44.1 kHz</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
