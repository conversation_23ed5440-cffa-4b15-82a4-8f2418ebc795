import React, { useState, useEffect } from 'react';
import {
  PaperAirplaneIcon,
  MicrophoneIcon,
  DownloadIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  InformationCircleIcon,
  SparklesIcon,
  ClockIcon,
  CpuChipIcon,
  CircleStackIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline';

interface Model {
  id: string;
  name: string;
  provider: string;
  model_type: string;
  description: string;
  size_gb: number;
  parameters?: number;
  architecture: string;
  license: string;
  tags: string[];
  download_url: string;
  homepage_url?: string;
  is_local: boolean;
  is_downloaded: boolean;
  available_quantizations: string[];
  context_length?: number;
  languages: string[];
  use_cases: string[];
  compatibility?: {
    rating: string;
    can_run: boolean;
    optimal_quantization: string;
    estimated_download_time_minutes: number;
    recommendations: string[];
    warnings: string[];
    alternative_models: string[];
  };
}

interface HardwareSpecs {
  cpu: {
    cores: number;
    threads: number;
    frequency_mhz: number;
    architecture: string;
  };
  memory: {
    total_gb: number;
    available_gb: number;
    usage_percent: number;
  };
  gpus: Array<{
    id: number;
    name: string;
    type: string;
    memory_total_mb: number;
    memory_used_mb: number;
    memory_free_mb: number;
    utilization_percent: number;
    temperature_celsius?: number;
    compute_capabilities: string[];
    driver_version?: string;
    cuda_version?: string;
  }>;
  platform: {
    system: string;
    release: string;
    python_version: string;
  };
  compute_capabilities: string[];
  ai_readiness: {
    cpu_score: number;
    memory_score: number;
    gpu_score: number;
    overall_score?: number;
  };
}

const chatHistory = [
  {
    id: 1,
    type: 'user',
    message: 'Hello! Can you help me organize my files?',
    timestamp: '2025-09-14T23:10:00Z',
  },
  {
    id: 2,
    type: 'assistant',
    message: 'Hello! I\'d be happy to help you organize your files. I can help you create automation tasks to sort files by type, date, or other criteria. What specific organization would you like to implement?',
    timestamp: '2025-09-14T23:10:05Z',
  },
];

export default function AI() {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'models' | 'hardware'>('models');

  // Model discovery state
  const [models, setModels] = useState<Model[]>([]);
  const [filteredModels, setFilteredModels] = useState<Model[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProviders, setSelectedProviders] = useState<string[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [maxSize, setMaxSize] = useState<number>(50);
  const [compatibilityFilter, setCompatibilityFilter] = useState<string>('all');

  // Hardware state
  const [hardwareSpecs, setHardwareSpecs] = useState<HardwareSpecs | null>(null);
  const [hardwareLoading, setHardwareLoading] = useState(false);

  // Load models on component mount
  useEffect(() => {
    loadModels();
    loadHardwareSpecs();
  }, []);

  // Filter models when search/filter criteria change
  useEffect(() => {
    filterModels();
  }, [models, searchQuery, selectedProviders, selectedTypes, maxSize, compatibilityFilter]);

  const loadModels = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/ai/models/discover?include_compatibility=true');
      const data = await response.json();

      // Flatten models from all providers
      const allModels: Model[] = [];
      Object.values(data.models).forEach((providerModels: any) => {
        allModels.push(...providerModels);
      });

      setModels(allModels);
    } catch (error) {
      console.error('Error loading models:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadHardwareSpecs = async () => {
    setHardwareLoading(true);
    try {
      const response = await fetch('/api/v1/ai/hardware/analysis');
      const data = await response.json();
      setHardwareSpecs(data);
    } catch (error) {
      console.error('Error loading hardware specs:', error);
    } finally {
      setHardwareLoading(false);
    }
  };

  const filterModels = () => {
    let filtered = models;

    // Search query filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(query) ||
        model.description.toLowerCase().includes(query) ||
        model.tags.some(tag => tag.toLowerCase().includes(query)) ||
        model.use_cases.some(useCase => useCase.toLowerCase().includes(query))
      );
    }

    // Provider filter
    if (selectedProviders.length > 0) {
      filtered = filtered.filter(model => selectedProviders.includes(model.provider));
    }

    // Type filter
    if (selectedTypes.length > 0) {
      filtered = filtered.filter(model => selectedTypes.includes(model.model_type));
    }

    // Size filter
    filtered = filtered.filter(model => model.size_gb <= maxSize);

    // Compatibility filter
    if (compatibilityFilter !== 'all') {
      filtered = filtered.filter(model => {
        if (!model.compatibility) return false;

        switch (compatibilityFilter) {
          case 'excellent':
            return model.compatibility.rating === 'excellent';
          case 'good':
            return model.compatibility.rating === 'good';
          case 'fair':
            return model.compatibility.rating === 'fair';
          case 'runnable':
            return model.compatibility.can_run;
          default:
            return true;
        }
      });
    }

    setFilteredModels(filtered);
  };

  const downloadModel = async (modelId: string, provider: string) => {
    try {
      const response = await fetch('/api/v1/ai/models/download', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ model_id: modelId, provider })
      });

      if (response.ok) {
        console.log('Model download started:', modelId);
        // TODO: Show download progress
      }
    } catch (error) {
      console.error('Error downloading model:', error);
    }
  };

  const handleSendMessage = () => {
    if (message.trim()) {
      // TODO: Implement message sending
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleVoiceRecord = () => {
    setIsRecording(!isRecording);
    // TODO: Implement voice recording
  };

  const getCompatibilityIcon = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'good':
        return <CheckCircleIcon className="h-5 w-5 text-blue-500" />;
      case 'fair':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'poor':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <InformationCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getCompatibilityColor = (rating: string) => {
    switch (rating) {
      case 'excellent':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'good':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatSize = (sizeGb: number) => {
    if (sizeGb < 1) {
      return `${Math.round(sizeGb * 1024)}MB`;
    }
    return `${sizeGb.toFixed(1)}GB`;
  };

  const formatParameters = (params?: number) => {
    if (!params) return 'Unknown';
    if (params >= 1000000000) {
      return `${(params / 1000000000).toFixed(1)}B`;
    }
    if (params >= 1000000) {
      return `${(params / 1000000).toFixed(1)}M`;
    }
    return params.toString();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">AI & Machine Learning</h1>
        <p className="mt-1 text-sm text-gray-500">
          Discover, analyze, and manage AI models with hardware compatibility insights
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'models', name: 'Model Discovery', icon: SparklesIcon },
            { id: 'hardware', name: 'Hardware Analysis', icon: CpuChipIcon },
            { id: 'chat', name: 'AI Chat', icon: PaperAirplaneIcon }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2`}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.name}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'models' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 input w-full"
                />
              </div>

              {/* Provider Filter */}
              <div>
                <select
                  multiple
                  value={selectedProviders}
                  onChange={(e) => setSelectedProviders(Array.from(e.target.selectedOptions, option => option.value))}
                  className="input w-full"
                >
                  <option value="">All Providers</option>
                  <option value="ollama">Ollama</option>
                  <option value="huggingface">HuggingFace</option>
                  <option value="github">GitHub</option>
                </select>
              </div>

              {/* Type Filter */}
              <div>
                <select
                  multiple
                  value={selectedTypes}
                  onChange={(e) => setSelectedTypes(Array.from(e.target.selectedOptions, option => option.value))}
                  className="input w-full"
                >
                  <option value="">All Types</option>
                  <option value="text_generation">Text Generation</option>
                  <option value="embeddings">Embeddings</option>
                  <option value="vision">Vision</option>
                  <option value="code">Code</option>
                  <option value="audio">Audio</option>
                </select>
              </div>

              {/* Compatibility Filter */}
              <div>
                <select
                  value={compatibilityFilter}
                  onChange={(e) => setCompatibilityFilter(e.target.value)}
                  className="input w-full"
                >
                  <option value="all">All Models</option>
                  <option value="excellent">Excellent</option>
                  <option value="good">Good</option>
                  <option value="fair">Fair</option>
                  <option value="runnable">Can Run</option>
                </select>
              </div>
            </div>

            {/* Size Filter */}
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Size: {formatSize(maxSize)}
              </label>
              <input
                type="range"
                min="0.1"
                max="100"
                step="0.1"
                value={maxSize}
                onChange={(e) => setMaxSize(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
          </div>

          {/* Models Grid */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">
                Available Models ({filteredModels.length})
              </h3>
              <button
                onClick={loadModels}
                disabled={loading}
                className="btn btn-primary"
              >
                {loading ? 'Loading...' : 'Refresh'}
              </button>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                <p className="mt-2 text-gray-500">Discovering models...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                {filteredModels.map((model) => (
                  <div key={model.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    {/* Model Header */}
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900 truncate">{model.name}</h4>
                        <p className="text-sm text-gray-500">{model.provider}</p>
                      </div>
                      {model.compatibility && (
                        <div className="flex items-center space-x-1">
                          {getCompatibilityIcon(model.compatibility.rating)}
                        </div>
                      )}
                    </div>

                    {/* Model Info */}
                    <div className="space-y-2 mb-4">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Size:</span>
                        <span className="font-medium">{formatSize(model.size_gb)}</span>
                      </div>
                      {model.parameters && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Parameters:</span>
                          <span className="font-medium">{formatParameters(model.parameters)}</span>
                        </div>
                      )}
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Type:</span>
                        <span className="font-medium capitalize">{model.model_type.replace('_', ' ')}</span>
                      </div>
                    </div>

                    {/* Compatibility Badge */}
                    {model.compatibility && (
                      <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border mb-3 ${getCompatibilityColor(model.compatibility.rating)}`}>
                        {model.compatibility.rating.charAt(0).toUpperCase() + model.compatibility.rating.slice(1)}
                      </div>
                    )}

                    {/* Description */}
                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">{model.description}</p>

                    {/* Tags */}
                    <div className="flex flex-wrap gap-1 mb-4">
                      {model.tags.slice(0, 3).map((tag) => (
                        <span key={tag} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          {tag}
                        </span>
                      ))}
                      {model.tags.length > 3 && (
                        <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                          +{model.tags.length - 3}
                        </span>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex space-x-2">
                      {model.is_downloaded ? (
                        <span className="flex items-center text-sm text-green-600">
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          Downloaded
                        </span>
                      ) : (
                        <button
                          onClick={() => downloadModel(model.id, model.provider)}
                          className="btn btn-sm btn-primary flex items-center"
                        >
                          <DownloadIcon className="h-4 w-4 mr-1" />
                          Download
                        </button>
                      )}
                      {model.homepage_url && (
                        <a
                          href={model.homepage_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="btn btn-sm btn-secondary"
                        >
                          View
                        </a>
                      )}
                    </div>

                    {/* Compatibility Details */}
                    {model.compatibility && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        {model.compatibility.estimated_download_time_minutes > 0 && (
                          <div className="flex items-center text-sm text-gray-500 mb-2">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            ~{Math.ceil(model.compatibility.estimated_download_time_minutes)} min download
                          </div>
                        )}
                        {model.compatibility.warnings.length > 0 && (
                          <div className="text-sm text-yellow-600">
                            <ExclamationTriangleIcon className="h-4 w-4 inline mr-1" />
                            {model.compatibility.warnings[0]}
                          </div>
                        )}
                        {model.compatibility.recommendations.length > 0 && (
                          <div className="text-sm text-blue-600">
                            <InformationCircleIcon className="h-4 w-4 inline mr-1" />
                            {model.compatibility.recommendations[0]}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'hardware' && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-medium text-gray-900">Hardware Analysis</h3>
            <button
              onClick={loadHardwareSpecs}
              disabled={hardwareLoading}
              className="btn btn-primary"
            >
              {hardwareLoading ? 'Analyzing...' : 'Refresh'}
            </button>
          </div>

          {hardwareLoading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-gray-500">Analyzing hardware...</p>
            </div>
          ) : hardwareSpecs ? (
            <div className="p-6 space-y-6">
              {/* AI Readiness Score */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                <h4 className="text-lg font-medium text-gray-900 mb-4">AI Readiness Score</h4>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{hardwareSpecs.ai_readiness.cpu_score}/10</div>
                    <div className="text-sm text-gray-600">CPU</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{hardwareSpecs.ai_readiness.memory_score.toFixed(1)}/10</div>
                    <div className="text-sm text-gray-600">Memory</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{hardwareSpecs.ai_readiness.gpu_score}/10</div>
                    <div className="text-sm text-gray-600">GPU</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-indigo-600">
                      {((hardwareSpecs.ai_readiness.cpu_score + hardwareSpecs.ai_readiness.memory_score + hardwareSpecs.ai_readiness.gpu_score) / 3).toFixed(1)}/10
                    </div>
                    <div className="text-sm text-gray-600">Overall</div>
                  </div>
                </div>
              </div>

              {/* Hardware Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* CPU */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <CpuChipIcon className="h-5 w-5 text-blue-500 mr-2" />
                    <h5 className="font-medium text-gray-900">CPU</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Cores:</span>
                      <span className="font-medium">{hardwareSpecs.cpu.cores}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Threads:</span>
                      <span className="font-medium">{hardwareSpecs.cpu.threads}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Frequency:</span>
                      <span className="font-medium">{(hardwareSpecs.cpu.frequency_mhz / 1000).toFixed(1)} GHz</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Architecture:</span>
                      <span className="font-medium">{hardwareSpecs.cpu.architecture}</span>
                    </div>
                  </div>
                </div>

                {/* Memory */}
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <CircleStackIcon className="h-5 w-5 text-green-500 mr-2" />
                    <h5 className="font-medium text-gray-900">Memory</h5>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Total:</span>
                      <span className="font-medium">{hardwareSpecs.memory.total_gb.toFixed(1)} GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Available:</span>
                      <span className="font-medium">{hardwareSpecs.memory.available_gb.toFixed(1)} GB</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Usage:</span>
                      <span className="font-medium">{hardwareSpecs.memory.usage_percent.toFixed(1)}%</span>
                    </div>
                  </div>
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full"
                        style={{ width: `${hardwareSpecs.memory.usage_percent}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* GPUs */}
              {hardwareSpecs.gpus.length > 0 && (
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center mb-3">
                    <ComputerDesktopIcon className="h-5 w-5 text-purple-500 mr-2" />
                    <h5 className="font-medium text-gray-900">Graphics Cards</h5>
                  </div>
                  <div className="space-y-4">
                    {hardwareSpecs.gpus.map((gpu) => (
                      <div key={gpu.id} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-2">
                          <h6 className="font-medium text-gray-900">{gpu.name}</h6>
                          <span className="text-sm text-gray-500 capitalize">{gpu.type}</span>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-500">Memory:</span>
                            <span className="font-medium">{(gpu.memory_total_mb / 1024).toFixed(1)} GB</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-500">Utilization:</span>
                            <span className="font-medium">{gpu.utilization_percent.toFixed(1)}%</span>
                          </div>
                          {gpu.temperature_celsius && (
                            <div className="flex justify-between">
                              <span className="text-gray-500">Temperature:</span>
                              <span className="font-medium">{gpu.temperature_celsius}°C</span>
                            </div>
                          )}
                          {gpu.driver_version && (
                            <div className="flex justify-between">
                              <span className="text-gray-500">Driver:</span>
                              <span className="font-medium">{gpu.driver_version}</span>
                            </div>
                          )}
                        </div>
                        <div className="mt-2">
                          <div className="flex flex-wrap gap-1">
                            {gpu.compute_capabilities.map((cap) => (
                              <span key={cap} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-purple-100 text-purple-800">
                                {cap.toUpperCase()}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Platform Info */}
              <div className="border border-gray-200 rounded-lg p-4">
                <h5 className="font-medium text-gray-900 mb-3">Platform Information</h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">System:</span>
                    <span className="font-medium">{hardwareSpecs.platform.system}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Release:</span>
                    <span className="font-medium">{hardwareSpecs.platform.release}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Python:</span>
                    <span className="font-medium">{hardwareSpecs.platform.python_version}</span>
                  </div>
                </div>
                <div className="mt-3">
                  <span className="text-gray-500 text-sm">Compute Capabilities:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {hardwareSpecs.compute_capabilities.map((cap) => (
                      <span key={cap} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                        {cap.toUpperCase()}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <ComputerDesktopIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>No hardware information available</p>
            </div>
          )}
        </div>
      )}

      {activeTab === 'chat' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-2">
          <div className="bg-white shadow rounded-lg h-96 flex flex-col">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">AI Chat</h3>
            </div>
            
            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  className={`flex ${chat.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      chat.type === 'user'
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    <p className="text-sm">{chat.message}</p>
                    <p className={`text-xs mt-1 ${
                      chat.type === 'user' ? 'text-primary-100' : 'text-gray-500'
                    }`}>
                      {new Date(chat.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {/* Chat Input */}
            <div className="border-t border-gray-200 p-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder="Type your message..."
                  className="input flex-1"
                />
                <button
                  onClick={handleVoiceRecord}
                  className={`p-2 rounded-md ${
                    isRecording
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <MicrophoneIcon className="h-5 w-5" />
                </button>
                <button
                  onClick={handleSendMessage}
                  className="btn-primary p-2"
                >
                  <PaperAirplaneIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Models Panel */}
        <div className="space-y-6">
          {/* Available Models */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Downloaded Models</h3>
            </div>
            <div className="p-4 space-y-3">
              {models.filter(model => model.is_downloaded).slice(0, 5).map((model) => (
                <div key={model.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{model.name}</p>
                      <p className="text-xs text-gray-500">{model.provider} • {model.model_type}</p>
                      <p className="text-xs text-gray-500">{formatSize(model.size_gb)}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {model.compatibility && getCompatibilityIcon(model.compatibility.rating)}
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Ready
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {models.filter(model => model.is_downloaded).length === 0 && (
                <p className="text-sm text-gray-500 text-center py-4">No models downloaded yet</p>
              )}
            </div>
          </div>

          {/* Download New Model */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Download Model</h3>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Model ID</label>
                <input
                  type="text"
                  placeholder="e.g., llama2:13b"
                  className="input mt-1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Provider</label>
                <select className="input mt-1">
                  <option value="ollama">Ollama</option>
                  <option value="huggingface">HuggingFace</option>
                  <option value="github">GitHub</option>
                </select>
              </div>
              <button className="btn-primary w-full flex items-center justify-center space-x-2">
                <DownloadIcon className="h-4 w-4" />
                <span>Download Model</span>
              </button>
            </div>
          </div>

          {/* Voice Settings */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Voice Settings</h3>
            </div>
            <div className="p-4 space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700">Language</label>
                <select className="input mt-1">
                  <option value="en-US">English (US)</option>
                  <option value="en-GB">English (UK)</option>
                  <option value="es-ES">Spanish</option>
                  <option value="fr-FR">French</option>
                  <option value="de-DE">German</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Sample Rate</label>
                <select className="input mt-1">
                  <option value="16000">16 kHz</option>
                  <option value="22050">22.05 kHz</option>
                  <option value="44100">44.1 kHz</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        )}
      </div>
    </div>
  );
}
