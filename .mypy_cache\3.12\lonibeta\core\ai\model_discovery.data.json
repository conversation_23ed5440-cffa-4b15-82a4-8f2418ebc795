{".class": "MypyFile", "_fullname": "lonibeta.core.ai.model_discovery", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ModelDiscoveryService": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService", "name": "ModelDiscoveryService", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "lonibeta.core.ai.model_discovery", "mro": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.__aenter__", "name": "__aenter__", "type": null}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc_val", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.__aexit__", "name": "__aexit__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.__init__", "name": "__init__", "type": null}}, "_discover_github_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._discover_github_models", "name": "_discover_github_models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_discover_github_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_discover_huggingface_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "model_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._discover_huggingface_models", "name": "_discover_huggingface_models", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "model_types"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelType"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_discover_huggingface_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_discover_ollama_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._discover_ollama_models", "name": "_discover_ollama_models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_discover_ollama_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_discover_provider_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "provider", "model_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._discover_provider_models", "name": "_discover_provider_models", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "provider", "model_types"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "lonibeta.core.ai.model_discovery.ModelProvider", {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelType"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_discover_provider_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TupleType", "implicit": false, "items": ["lonibeta.core.ai.model_discovery.ModelProvider", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_estimate_huggingface_model_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_data", "model_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._estimate_huggingface_model_size", "name": "_estimate_huggingface_model_size", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_data", "model_type"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "lonibeta.core.ai.model_discovery.ModelType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_estimate_huggingface_model_size of ModelDiscoveryService", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_huggingface_model_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_name", "model_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._get_huggingface_model_info", "name": "_get_huggingface_model_info", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_name", "model_type"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "builtins.str", "lonibeta.core.ai.model_discovery.ModelType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_huggingface_model_info of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["lonibeta.core.ai.model_discovery.ModelInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_ollama_remote_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._get_ollama_remote_models", "name": "_get_ollama_remote_models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_ollama_remote_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_cache_valid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "provider"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._is_cache_valid", "name": "_is_cache_valid", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "provider"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "lonibeta.core.ai.model_discovery.ModelProvider"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_cache_valid of ModelDiscoveryService", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_huggingface_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_data", "model_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._parse_huggingface_model", "name": "_parse_huggingface_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model_data", "model_type"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "lonibeta.core.ai.model_discovery.ModelType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_huggingface_model of ModelDiscoveryService", "ret_type": {".class": "UnionType", "items": ["lonibeta.core.ai.model_discovery.ModelInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parse_ollama_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_data", "is_local"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._parse_ollama_model", "name": "_parse_ollama_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "model_data", "is_local"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_ollama_model of ModelDiscoveryService", "ret_type": {".class": "UnionType", "items": ["lonibeta.core.ai.model_discovery.ModelInfo", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_update_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "provider", "models"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService._update_cache", "name": "_update_cache", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "provider", "models"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "lonibeta.core.ai.model_discovery.ModelProvider", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_update_cache of ModelDiscoveryService", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.cache", "name": "cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "cache_duration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.cache_duration", "name": "cache_duration", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cache_expiry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.cache_expiry", "name": "cache_expiry", "type": {".class": "Instance", "args": ["builtins.str", "datetime.datetime"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "discover_all_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "providers", "model_types", "use_cache"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.discover_all_models", "name": "discover_all_models", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "providers", "model_types", "use_cache"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelProvider"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelType"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "discover_all_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelProvider", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "providers", "model_types", "max_size_gb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.search_models", "name": "search_models", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "providers", "model_types", "max_size_gb"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelDiscoveryService", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelProvider"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelType"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_models of ModelDiscoveryService", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.ModelInfo"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.session", "name": "session", "type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "lonibeta.core.ai.model_discovery.aiohttp", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.model_discovery.ModelDiscoveryService.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.model_discovery.ModelDiscoveryService", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.model_discovery.ModelInfo", "name": "ModelInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "provider", "type": "lonibeta.core.ai.model_discovery.ModelProvider"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "model_type", "type": "lonibeta.core.ai.model_discovery.ModelType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "description", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "size_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 52, "name": "parameters", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 53, "name": "architecture", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "license", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "tags", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "download_url", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "homepage_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "paper_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "is_local", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "is_downloaded", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "download_progress", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 66, "name": "available_quantizations", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 67, "name": "default_quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "context_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 71, "name": "languages", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 72, "name": "use_cases", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "provider_metadata", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 78, "name": "created_at", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 79, "name": "updated_at", "type": "datetime.datetime"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 80, "name": "last_checked", "type": "datetime.datetime"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lonibeta.core.ai.model_discovery", "mro": ["lonibeta.core.ai.model_discovery.ModelInfo", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "id", "name", "provider", "model_type", "description", "size_gb", "parameters", "architecture", "license", "tags", "download_url", "homepage_url", "paper_url", "is_local", "is_downloaded", "download_progress", "available_quantizations", "default_quantization", "context_length", "languages", "use_cases", "provider_metadata", "created_at", "updated_at", "last_checked"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "id", "name", "provider", "model_type", "description", "size_gb", "parameters", "architecture", "license", "tags", "download_url", "homepage_url", "paper_url", "is_local", "is_downloaded", "download_progress", "available_quantizations", "default_quantization", "context_length", "languages", "use_cases", "provider_metadata", "created_at", "updated_at", "last_checked"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelInfo", "builtins.str", "builtins.str", "lonibeta.core.ai.model_discovery.ModelProvider", "lonibeta.core.ai.model_discovery.ModelType", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "datetime.datetime", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "provider"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "description"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "size_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "architecture"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "license"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tags"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "download_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "homepage_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "paper_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_local"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_downloaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "download_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "available_quantizations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default_quantization"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "context_length"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "languages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "use_cases"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "provider_metadata"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "last_checked"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-post_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__mypy-post_init", "name": "__mypy-post_init", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["lonibeta.core.ai.model_discovery.ModelInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-post_init of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["id", "name", "provider", "model_type", "description", "size_gb", "parameters", "architecture", "license", "tags", "download_url", "homepage_url", "paper_url", "is_local", "is_downloaded", "download_progress", "available_quantizations", "default_quantization", "context_length", "languages", "use_cases", "provider_metadata", "created_at", "updated_at", "last_checked"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["id", "name", "provider", "model_type", "description", "size_gb", "parameters", "architecture", "license", "tags", "download_url", "homepage_url", "paper_url", "is_local", "is_downloaded", "download_progress", "available_quantizations", "default_quantization", "context_length", "languages", "use_cases", "provider_metadata", "created_at", "updated_at", "last_checked"], "arg_types": ["builtins.str", "builtins.str", "lonibeta.core.ai.model_discovery.ModelProvider", "lonibeta.core.ai.model_discovery.ModelType", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "datetime.datetime", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["id", "name", "provider", "model_type", "description", "size_gb", "parameters", "architecture", "license", "tags", "download_url", "homepage_url", "paper_url", "is_local", "is_downloaded", "download_progress", "available_quantizations", "default_quantization", "context_length", "languages", "use_cases", "provider_metadata", "created_at", "updated_at", "last_checked"], "arg_types": ["builtins.str", "builtins.str", "lonibeta.core.ai.model_discovery.ModelProvider", "lonibeta.core.ai.model_discovery.ModelType", "builtins.str", "builtins.float", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.float", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "datetime.datetime", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ModelInfo", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "__post_init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.__post_init__", "name": "__post_init__", "type": null}}, "architecture": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.architecture", "name": "architecture", "type": "builtins.str"}}, "available_quantizations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.available_quantizations", "name": "available_quantizations", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "context_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.context_length", "name": "context_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.created_at", "name": "created_at", "type": "datetime.datetime"}}, "default_quantization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.default_quantization", "name": "default_quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}}, "description": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.description", "name": "description", "type": "builtins.str"}}, "download_progress": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.download_progress", "name": "download_progress", "type": "builtins.float"}}, "download_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.download_url", "name": "download_url", "type": "builtins.str"}}, "homepage_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.homepage_url", "name": "homepage_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.id", "name": "id", "type": "builtins.str"}}, "is_downloaded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.is_downloaded", "name": "is_downloaded", "type": "builtins.bool"}}, "is_local": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.is_local", "name": "is_local", "type": "builtins.bool"}}, "languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.languages", "name": "languages", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "last_checked": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.last_checked", "name": "last_checked", "type": "datetime.datetime"}}, "license": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.license", "name": "license", "type": "builtins.str"}}, "model_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.model_type", "name": "model_type", "type": "lonibeta.core.ai.model_discovery.ModelType"}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.name", "name": "name", "type": "builtins.str"}}, "paper_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.paper_url", "name": "paper_url", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parameters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.parameters", "name": "parameters", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "provider": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.provider", "name": "provider", "type": "lonibeta.core.ai.model_discovery.ModelProvider"}}, "provider_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.provider_metadata", "name": "provider_metadata", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "size_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.size_gb", "name": "size_gb", "type": "builtins.float"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.tags", "name": "tags", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "updated_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.updated_at", "name": "updated_at", "type": "datetime.datetime"}}, "use_cases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.use_cases", "name": "use_cases", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.model_discovery.ModelInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.model_discovery.ModelInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelProvider": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.model_discovery.ModelProvider", "name": "Model<PERSON>rovider", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "lonibeta.core.ai.model_discovery.ModelProvider", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "lonibeta.core.ai.model_discovery", "mro": ["lonibeta.core.ai.model_discovery.ModelProvider", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "GITHUB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelProvider.GITHUB", "name": "GITHUB", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "github"}, "type_ref": "builtins.str"}}}, "HUGGINGFACE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelProvider.HUGGINGFACE", "name": "HUGGINGFACE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "huggingface"}, "type_ref": "builtins.str"}}}, "OLLAMA": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelProvider.OLLAMA", "name": "OLLAMA", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ollama"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.model_discovery.ModelProvider.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.model_discovery.ModelProvider", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.model_discovery.ModelType", "name": "ModelType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "lonibeta.core.ai.model_discovery.ModelType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "lonibeta.core.ai.model_discovery", "mro": ["lonibeta.core.ai.model_discovery.ModelType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AUDIO": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.AUDIO", "name": "AUDIO", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "audio"}, "type_ref": "builtins.str"}}}, "CODE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.CODE", "name": "CODE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "code"}, "type_ref": "builtins.str"}}}, "EMBEDDINGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.EMBEDDINGS", "name": "EMBEDDINGS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "embeddings"}, "type_ref": "builtins.str"}}}, "MULTIMODAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.MULTIMODAL", "name": "MULTIMODAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "multimodal"}, "type_ref": "builtins.str"}}}, "TEXT_GENERATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.TEXT_GENERATION", "name": "TEXT_GENERATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "text_generation"}, "type_ref": "builtins.str"}}}, "VISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.ModelType.VISION", "name": "VISION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.model_discovery.ModelType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.model_discovery.ModelType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "QuantizationType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.model_discovery.QuantizationType", "name": "QuantizationType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "lonibeta.core.ai.model_discovery.QuantizationType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "lonibeta.core.ai.model_discovery", "mro": ["lonibeta.core.ai.model_discovery.QuantizationType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FP16": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.QuantizationType.FP16", "name": "FP16", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "fp16"}, "type_ref": "builtins.str"}}}, "FP32": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.QuantizationType.FP32", "name": "FP32", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "fp32"}, "type_ref": "builtins.str"}}}, "INT4": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.QuantizationType.INT4", "name": "INT4", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "int4"}, "type_ref": "builtins.str"}}}, "INT8": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.QuantizationType.INT8", "name": "INT8", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "int8"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.model_discovery.QuantizationType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.model_discovery.QuantizationType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.model_discovery.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "aiohttp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "lonibeta.core.ai.model_discovery.aiohttp", "name": "aiohttp", "type": {".class": "AnyType", "missing_import_name": "lonibeta.core.ai.model_discovery.aiohttp", "source_any": null, "type_of_any": 3}}}, "asdict": {".class": "SymbolTableNode", "cross_ref": "dataclasses.asdict", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.model_discovery.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\core\\ai\\model_discovery.py"}