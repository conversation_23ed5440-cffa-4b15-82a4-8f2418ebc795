"""
Privacy-focused local data management.
"""

import os
import json
import sqlite3
import aiosqlite
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
import hashlib
import uuid

logger = logging.getLogger(__name__)


@dataclass
class DataRecord:
    """Generic data record structure."""
    id: str
    type: str
    data: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class DataManager:
    """Privacy-focused local data management."""

    def __init__(self, data_dir: str = "./data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Database file
        self.db_path = self.data_dir / "lonibeta.db"
        
        # File storage directory
        self.file_storage_dir = self.data_dir / "files"
        self.file_storage_dir.mkdir(exist_ok=True)

    async def initialize(self) -> None:
        """Initialize the data manager and create tables."""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                CREATE TABLE IF NOT EXISTS data_records (
                    id TEXT PRIMARY KEY,
                    type TEXT NOT NULL,
                    data TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    expires_at TEXT,
                    metadata TEXT
                )
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_data_records_type 
                ON data_records(type)
            """)
            
            await db.execute("""
                CREATE INDEX IF NOT EXISTS idx_data_records_expires_at 
                ON data_records(expires_at)
            """)
            
            await db.commit()

        logger.info("Data manager initialized")

    async def store_data(
        self,
        data_type: str,
        data: Dict[str, Any],
        record_id: Optional[str] = None,
        expires_in_hours: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store data locally."""
        record_id = record_id or str(uuid.uuid4())
        now = datetime.now()
        expires_at = now + timedelta(hours=expires_in_hours) if expires_in_hours else None

        record = DataRecord(
            id=record_id,
            type=data_type,
            data=data,
            created_at=now,
            updated_at=now,
            expires_at=expires_at,
            metadata=metadata or {}
        )

        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                INSERT OR REPLACE INTO data_records 
                (id, type, data, created_at, updated_at, expires_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                record.id,
                record.type,
                json.dumps(record.data),
                record.created_at.isoformat(),
                record.updated_at.isoformat(),
                record.expires_at.isoformat() if record.expires_at else None,
                json.dumps(record.metadata)
            ))
            await db.commit()

        logger.info(f"Stored data record: {record_id} (type: {data_type})")
        return record_id

    async def get_data(self, record_id: str) -> Optional[DataRecord]:
        """Retrieve data by ID."""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT id, type, data, created_at, updated_at, expires_at, metadata
                FROM data_records WHERE id = ?
            """, (record_id,)) as cursor:
                row = await cursor.fetchone()

        if not row:
            return None

        # Check if record has expired
        expires_at = datetime.fromisoformat(row[5]) if row[5] else None
        if expires_at and expires_at < datetime.now():
            await self.delete_data(record_id)
            return None

        return DataRecord(
            id=row[0],
            type=row[1],
            data=json.loads(row[2]),
            created_at=datetime.fromisoformat(row[3]),
            updated_at=datetime.fromisoformat(row[4]),
            expires_at=expires_at,
            metadata=json.loads(row[6]) if row[6] else {}
        )

    async def get_data_by_type(
        self,
        data_type: str,
        limit: int = 100,
        offset: int = 0
    ) -> List[DataRecord]:
        """Retrieve data records by type."""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT id, type, data, created_at, updated_at, expires_at, metadata
                FROM data_records 
                WHERE type = ? 
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """, (data_type, limit, offset)) as cursor:
                rows = await cursor.fetchall()

        records = []
        expired_ids = []

        for row in rows:
            expires_at = datetime.fromisoformat(row[5]) if row[5] else None
            
            # Check if record has expired
            if expires_at and expires_at < datetime.now():
                expired_ids.append(row[0])
                continue

            records.append(DataRecord(
                id=row[0],
                type=row[1],
                data=json.loads(row[2]),
                created_at=datetime.fromisoformat(row[3]),
                updated_at=datetime.fromisoformat(row[4]),
                expires_at=expires_at,
                metadata=json.loads(row[6]) if row[6] else {}
            ))

        # Clean up expired records
        if expired_ids:
            await self._delete_multiple_records(expired_ids)

        return records

    async def update_data(
        self,
        record_id: str,
        data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Update existing data record."""
        existing_record = await self.get_data(record_id)
        if not existing_record:
            return False

        now = datetime.now()

        async with aiosqlite.connect(self.db_path) as db:
            await db.execute("""
                UPDATE data_records 
                SET data = ?, updated_at = ?, metadata = ?
                WHERE id = ?
            """, (
                json.dumps(data),
                now.isoformat(),
                json.dumps(metadata) if metadata else json.dumps(existing_record.metadata),
                record_id
            ))
            await db.commit()

        logger.info(f"Updated data record: {record_id}")
        return True

    async def delete_data(self, record_id: str) -> bool:
        """Delete data record."""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                DELETE FROM data_records WHERE id = ?
            """, (record_id,))
            await db.commit()
            
            deleted = cursor.rowcount > 0

        if deleted:
            logger.info(f"Deleted data record: {record_id}")

        return deleted

    async def _delete_multiple_records(self, record_ids: List[str]) -> None:
        """Delete multiple records efficiently."""
        if not record_ids:
            return

        placeholders = ','.join('?' * len(record_ids))
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(f"""
                DELETE FROM data_records WHERE id IN ({placeholders})
            """, record_ids)
            await db.commit()

        logger.info(f"Deleted {len(record_ids)} expired records")

    async def cleanup_expired_data(self) -> int:
        """Clean up expired data records."""
        now = datetime.now().isoformat()
        
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute("""
                DELETE FROM data_records 
                WHERE expires_at IS NOT NULL AND expires_at < ?
            """, (now,))
            await db.commit()
            
            deleted_count = cursor.rowcount

        if deleted_count > 0:
            logger.info(f"Cleaned up {deleted_count} expired records")

        return deleted_count

    async def store_file(
        self,
        file_data: bytes,
        filename: str,
        content_type: str = "application/octet-stream",
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Store file data locally."""
        file_id = str(uuid.uuid4())
        file_hash = hashlib.sha256(file_data).hexdigest()
        
        # Create file path
        file_path = self.file_storage_dir / f"{file_id}_{filename}"
        
        # Write file
        with open(file_path, 'wb') as f:
            f.write(file_data)

        # Store file metadata
        file_metadata = {
            "filename": filename,
            "content_type": content_type,
            "size": len(file_data),
            "hash": file_hash,
            "path": str(file_path),
            **(metadata or {})
        }

        await self.store_data("file", file_metadata, file_id)
        
        logger.info(f"Stored file: {filename} ({file_id})")
        return file_id

    async def get_file(self, file_id: str) -> Optional[tuple[bytes, Dict[str, Any]]]:
        """Retrieve file data and metadata."""
        record = await self.get_data(file_id)
        if not record or record.type != "file":
            return None

        file_path = Path(record.data["path"])
        if not file_path.exists():
            # File missing, clean up record
            await self.delete_data(file_id)
            return None

        with open(file_path, 'rb') as f:
            file_data = f.read()

        return file_data, record.data

    async def delete_file(self, file_id: str) -> bool:
        """Delete file and its metadata."""
        record = await self.get_data(file_id)
        if not record or record.type != "file":
            return False

        # Delete physical file
        file_path = Path(record.data["path"])
        if file_path.exists():
            file_path.unlink()

        # Delete record
        return await self.delete_data(file_id)

    async def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        async with aiosqlite.connect(self.db_path) as db:
            # Count records by type
            async with db.execute("""
                SELECT type, COUNT(*) FROM data_records GROUP BY type
            """) as cursor:
                type_counts = dict(await cursor.fetchall())

            # Total records
            async with db.execute("""
                SELECT COUNT(*) FROM data_records
            """) as cursor:
                total_records = (await cursor.fetchone())[0]

            # Expired records
            now = datetime.now().isoformat()
            async with db.execute("""
                SELECT COUNT(*) FROM data_records 
                WHERE expires_at IS NOT NULL AND expires_at < ?
            """, (now,)) as cursor:
                expired_records = (await cursor.fetchone())[0]

        # File storage size
        file_storage_size = sum(
            f.stat().st_size for f in self.file_storage_dir.rglob('*') if f.is_file()
        )

        # Database size
        db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

        return {
            "total_records": total_records,
            "expired_records": expired_records,
            "records_by_type": type_counts,
            "database_size_bytes": db_size,
            "file_storage_size_bytes": file_storage_size,
            "total_storage_size_bytes": db_size + file_storage_size
        }
