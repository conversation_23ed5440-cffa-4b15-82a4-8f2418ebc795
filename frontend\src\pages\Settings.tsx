import React, { useState } from 'react';
import { SaveIcon } from '@heroicons/react/24/outline';

export default function Settings() {
  const [settings, setSettings] = useState({
    // General
    theme: 'light',
    language: 'en',
    autoSave: true,
    
    // Automation
    maxConcurrentTasks: 5,
    taskTimeout: 300,
    enableNotifications: true,
    
    // AI/ML
    defaultModel: 'llama2-7b',
    maxTokens: 2048,
    temperature: 0.7,
    
    // Code Engine
    defaultLanguage: 'python',
    codeTimeout: 30,
    enableValidation: true,
    
    // Monitoring
    refreshInterval: 5,
    retentionDays: 30,
    alertThresholds: {
      cpu: 80,
      memory: 85,
      disk: 90,
    },
    
    // Security
    sessionTimeout: 30,
    enableTwoFactor: false,
    logLevel: 'INFO',
  });

  const handleSave = () => {
    // TODO: Implement settings save
    console.log('Saving settings:', settings);
  };

  const updateSetting = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const updateNestedSetting = (parent: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure your LoniBeta platform preferences
          </p>
        </div>
        <button onClick={handleSave} className="btn-primary flex items-center space-x-2">
          <SaveIcon className="h-5 w-5" />
          <span>Save Changes</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">General</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Theme</label>
              <select
                value={settings.theme}
                onChange={(e) => updateSetting('theme', e.target.value)}
                className="input mt-1"
              >
                <option value="light">Light</option>
                <option value="dark">Dark</option>
                <option value="auto">Auto</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Language</label>
              <select
                value={settings.language}
                onChange={(e) => updateSetting('language', e.target.value)}
                className="input mt-1"
              >
                <option value="en">English</option>
                <option value="es">Spanish</option>
                <option value="fr">French</option>
                <option value="de">German</option>
              </select>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.autoSave}
                onChange={(e) => updateSetting('autoSave', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Enable auto-save
              </label>
            </div>
          </div>
        </div>

        {/* Automation Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Automation</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Max Concurrent Tasks
              </label>
              <input
                type="number"
                value={settings.maxConcurrentTasks}
                onChange={(e) => updateSetting('maxConcurrentTasks', parseInt(e.target.value))}
                className="input mt-1"
                min="1"
                max="20"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Task Timeout (seconds)
              </label>
              <input
                type="number"
                value={settings.taskTimeout}
                onChange={(e) => updateSetting('taskTimeout', parseInt(e.target.value))}
                className="input mt-1"
                min="30"
                max="3600"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enableNotifications}
                onChange={(e) => updateSetting('enableNotifications', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Enable notifications
              </label>
            </div>
          </div>
        </div>

        {/* AI/ML Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">AI & Machine Learning</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Default Model</label>
              <select
                value={settings.defaultModel}
                onChange={(e) => updateSetting('defaultModel', e.target.value)}
                className="input mt-1"
              >
                <option value="llama2-7b">Llama 2 7B</option>
                <option value="mistral-7b">Mistral 7B</option>
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Max Tokens
              </label>
              <input
                type="number"
                value={settings.maxTokens}
                onChange={(e) => updateSetting('maxTokens', parseInt(e.target.value))}
                className="input mt-1"
                min="100"
                max="8192"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Temperature ({settings.temperature})
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={settings.temperature}
                onChange={(e) => updateSetting('temperature', parseFloat(e.target.value))}
                className="w-full mt-1"
              />
            </div>
          </div>
        </div>

        {/* Code Engine Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Code Engine</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Default Language</label>
              <select
                value={settings.defaultLanguage}
                onChange={(e) => updateSetting('defaultLanguage', e.target.value)}
                className="input mt-1"
              >
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="typescript">TypeScript</option>
                <option value="bash">Bash</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Code Execution Timeout (seconds)
              </label>
              <input
                type="number"
                value={settings.codeTimeout}
                onChange={(e) => updateSetting('codeTimeout', parseInt(e.target.value))}
                className="input mt-1"
                min="5"
                max="300"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enableValidation}
                onChange={(e) => updateSetting('enableValidation', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Enable code validation
              </label>
            </div>
          </div>
        </div>

        {/* Monitoring Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Monitoring</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Refresh Interval (seconds)
              </label>
              <input
                type="number"
                value={settings.refreshInterval}
                onChange={(e) => updateSetting('refreshInterval', parseInt(e.target.value))}
                className="input mt-1"
                min="1"
                max="60"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Data Retention (days)
              </label>
              <input
                type="number"
                value={settings.retentionDays}
                onChange={(e) => updateSetting('retentionDays', parseInt(e.target.value))}
                className="input mt-1"
                min="1"
                max="365"
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">Alert Thresholds (%)</label>
              <div className="grid grid-cols-3 gap-2">
                <div>
                  <label className="block text-xs text-gray-500">CPU</label>
                  <input
                    type="number"
                    value={settings.alertThresholds.cpu}
                    onChange={(e) => updateNestedSetting('alertThresholds', 'cpu', parseInt(e.target.value))}
                    className="input text-sm"
                    min="50"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500">Memory</label>
                  <input
                    type="number"
                    value={settings.alertThresholds.memory}
                    onChange={(e) => updateNestedSetting('alertThresholds', 'memory', parseInt(e.target.value))}
                    className="input text-sm"
                    min="50"
                    max="100"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500">Disk</label>
                  <input
                    type="number"
                    value={settings.alertThresholds.disk}
                    onChange={(e) => updateNestedSetting('alertThresholds', 'disk', parseInt(e.target.value))}
                    className="input text-sm"
                    min="50"
                    max="100"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Security</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
                className="input mt-1"
                min="5"
                max="480"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.enableTwoFactor}
                onChange={(e) => updateSetting('enableTwoFactor', e.target.checked)}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label className="ml-2 block text-sm text-gray-900">
                Enable two-factor authentication
              </label>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Log Level</label>
              <select
                value={settings.logLevel}
                onChange={(e) => updateSetting('logLevel', e.target.value)}
                className="input mt-1"
              >
                <option value="DEBUG">Debug</option>
                <option value="INFO">Info</option>
                <option value="WARNING">Warning</option>
                <option value="ERROR">Error</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
