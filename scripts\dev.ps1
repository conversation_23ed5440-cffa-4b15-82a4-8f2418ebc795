# LoniBeta Development Script for Windows PowerShell

Write-Host "🚀 Starting LoniBeta Development Environment" -ForegroundColor Green

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check prerequisites
Write-Host "📋 Checking prerequisites..." -ForegroundColor Yellow

if (-not (Test-Command "python")) {
    Write-Host "❌ Python is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "node")) {
    Write-Host "❌ Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

if (-not (Test-Command "uv")) {
    Write-Host "❌ uv is not installed. Please install it first:" -ForegroundColor Red
    Write-Host "   curl -LsSf https://astral.sh/uv/install.sh | sh" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Command "bun")) {
    Write-Host "❌ bun is not installed. Please install it first:" -ForegroundColor Red
    Write-Host "   curl -fsSL https://bun.sh/install | bash" -ForegroundColor Yellow
    exit 1
}

Write-Host "✅ All prerequisites are installed" -ForegroundColor Green

# Setup backend
Write-Host "🐍 Setting up Python backend..." -ForegroundColor Yellow
Set-Location backend

if (-not (Test-Path ".venv")) {
    Write-Host "Creating Python virtual environment..." -ForegroundColor Cyan
    uv venv
}

Write-Host "Installing Python dependencies..." -ForegroundColor Cyan
uv sync

# Copy environment file if it doesn't exist
if (-not (Test-Path ".env")) {
    Write-Host "Creating .env file from template..." -ForegroundColor Cyan
    Copy-Item ".env.example" ".env"
    Write-Host "⚠️  Please edit .env file with your configuration" -ForegroundColor Yellow
}

Set-Location ..

# Setup frontend
Write-Host "⚛️  Setting up React frontend..." -ForegroundColor Yellow
Set-Location frontend

Write-Host "Installing Node.js dependencies..." -ForegroundColor Cyan
bun install

Set-Location ..

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @("logs", "uploads", "data")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "Created directory: $dir" -ForegroundColor Cyan
    }
}

Write-Host "🎉 Development environment setup complete!" -ForegroundColor Green
Write-Host ""
Write-Host "To start the development servers:" -ForegroundColor Yellow
Write-Host "  Backend:  .\scripts\start-backend.ps1" -ForegroundColor Cyan
Write-Host "  Frontend: .\scripts\start-frontend.ps1" -ForegroundColor Cyan
Write-Host "  Both:     .\scripts\start-all.ps1" -ForegroundColor Cyan
