"""
AI model discovery service for multiple providers.
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import re

logger = logging.getLogger(__name__)


class ModelProvider(Enum):
    """AI model providers."""
    OLLAMA = "ollama"
    HUGGINGFACE = "huggingface"
    GITHUB = "github"


class ModelType(Enum):
    """Types of AI models."""
    TEXT_GENERATION = "text_generation"
    EMBEDDINGS = "embeddings"
    VISION = "vision"
    AUDIO = "audio"
    MULTIMODAL = "multimodal"
    CODE = "code"


class QuantizationType(Enum):
    """Model quantization types."""
    FP32 = "fp32"
    FP16 = "fp16"
    INT8 = "int8"
    INT4 = "int4"


@dataclass
class ModelInfo:
    """Comprehensive model information."""
    id: str
    name: str
    provider: ModelProvider
    model_type: ModelType
    description: str
    size_gb: float
    parameters: Optional[int]
    architecture: str
    license: str
    tags: List[str]
    download_url: str
    homepage_url: Optional[str]
    paper_url: Optional[str]
    
    # Availability
    is_local: bool = False
    is_downloaded: bool = False
    download_progress: float = 0.0
    
    # Quantization options
    available_quantizations: List[QuantizationType] = None
    default_quantization: QuantizationType = QuantizationType.FP16
    
    # Performance metadata
    context_length: Optional[int] = None
    languages: List[str] = None
    use_cases: List[str] = None
    
    # Provider-specific metadata
    provider_metadata: Dict[str, Any] = None
    
    # Timestamps
    created_at: datetime = None
    updated_at: datetime = None
    last_checked: datetime = None

    def __post_init__(self):
        if self.available_quantizations is None:
            self.available_quantizations = [QuantizationType.FP16]
        if self.languages is None:
            self.languages = ["en"]
        if self.use_cases is None:
            self.use_cases = []
        if self.provider_metadata is None:
            self.provider_metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if self.last_checked is None:
            self.last_checked = datetime.now()


class ModelDiscoveryService:
    """Service for discovering AI models from multiple providers."""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.cache: Dict[str, List[ModelInfo]] = {}
        self.cache_expiry: Dict[str, datetime] = {}
        self.cache_duration = timedelta(hours=6)  # Cache for 6 hours

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def discover_all_models(
        self,
        providers: Optional[List[ModelProvider]] = None,
        model_types: Optional[List[ModelType]] = None,
        use_cache: bool = True
    ) -> Dict[ModelProvider, List[ModelInfo]]:
        """Discover models from all specified providers."""
        if providers is None:
            providers = list(ModelProvider)

        results = {}
        tasks = []

        for provider in providers:
            if use_cache and self._is_cache_valid(provider):
                results[provider] = self.cache[provider.value]
            else:
                tasks.append(self._discover_provider_models(provider, model_types))

        if tasks:
            provider_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for i, result in enumerate(provider_results):
                provider = providers[i] if i < len(providers) else None
                if isinstance(result, Exception):
                    logger.error(f"Error discovering models from {provider}: {result}")
                    results[provider] = []
                else:
                    provider, models = result
                    results[provider] = models
                    self._update_cache(provider, models)

        return results

    async def _discover_provider_models(
        self,
        provider: ModelProvider,
        model_types: Optional[List[ModelType]] = None
    ) -> tuple[ModelProvider, List[ModelInfo]]:
        """Discover models from a specific provider."""
        try:
            if provider == ModelProvider.OLLAMA:
                models = await self._discover_ollama_models()
            elif provider == ModelProvider.HUGGINGFACE:
                models = await self._discover_huggingface_models(model_types)
            elif provider == ModelProvider.GITHUB:
                models = await self._discover_github_models()
            else:
                models = []

            logger.info(f"Discovered {len(models)} models from {provider.value}")
            return provider, models

        except Exception as e:
            logger.error(f"Error discovering models from {provider.value}: {e}")
            return provider, []

    async def _discover_ollama_models(self) -> List[ModelInfo]:
        """Discover models from Ollama."""
        models = []
        
        try:
            # Get local models
            async with self.session.get("http://localhost:11434/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    for model_data in data.get("models", []):
                        model = self._parse_ollama_model(model_data, is_local=True)
                        if model:
                            models.append(model)

            # Get available remote models (popular ones)
            remote_models = await self._get_ollama_remote_models()
            models.extend(remote_models)

        except aiohttp.ClientError as e:
            logger.warning(f"Could not connect to Ollama: {e}")

        return models

    async def _get_ollama_remote_models(self) -> List[ModelInfo]:
        """Get popular Ollama models from their registry."""
        # Popular Ollama models with their metadata
        popular_models = [
            {
                "name": "llama2:7b",
                "size_gb": 3.8,
                "parameters": 7000000000,
                "description": "Meta's Llama 2 7B model for text generation",
                "architecture": "Llama",
                "model_type": ModelType.TEXT_GENERATION,
                "context_length": 4096,
                "use_cases": ["chat", "text_generation", "instruction_following"]
            },
            {
                "name": "llama2:13b",
                "size_gb": 7.3,
                "parameters": 13000000000,
                "description": "Meta's Llama 2 13B model for text generation",
                "architecture": "Llama",
                "model_type": ModelType.TEXT_GENERATION,
                "context_length": 4096,
                "use_cases": ["chat", "text_generation", "instruction_following"]
            },
            {
                "name": "mistral:7b",
                "size_gb": 4.1,
                "parameters": 7000000000,
                "description": "Mistral 7B model optimized for efficiency",
                "architecture": "Mistral",
                "model_type": ModelType.TEXT_GENERATION,
                "context_length": 8192,
                "use_cases": ["chat", "text_generation", "code_generation"]
            },
            {
                "name": "codellama:7b",
                "size_gb": 3.8,
                "parameters": 7000000000,
                "description": "Code Llama 7B specialized for code generation",
                "architecture": "Llama",
                "model_type": ModelType.CODE,
                "context_length": 16384,
                "use_cases": ["code_generation", "code_completion", "debugging"]
            },
            {
                "name": "nomic-embed-text",
                "size_gb": 0.27,
                "parameters": 137000000,
                "description": "Nomic Embed text embedding model",
                "architecture": "BERT",
                "model_type": ModelType.EMBEDDINGS,
                "context_length": 8192,
                "use_cases": ["embeddings", "semantic_search", "similarity"]
            }
        ]

        models = []
        for model_data in popular_models:
            model = ModelInfo(
                id=f"ollama:{model_data['name']}",
                name=model_data["name"],
                provider=ModelProvider.OLLAMA,
                model_type=model_data["model_type"],
                description=model_data["description"],
                size_gb=model_data["size_gb"],
                parameters=model_data["parameters"],
                architecture=model_data["architecture"],
                license="Custom",
                tags=["ollama", model_data["architecture"].lower()],
                download_url=f"ollama pull {model_data['name']}",
                context_length=model_data.get("context_length"),
                use_cases=model_data.get("use_cases", []),
                available_quantizations=[QuantizationType.FP16, QuantizationType.INT8, QuantizationType.INT4],
                provider_metadata={"ollama_name": model_data["name"]}
            )
            models.append(model)

        return models

    def _parse_ollama_model(self, model_data: Dict[str, Any], is_local: bool = False) -> Optional[ModelInfo]:
        """Parse Ollama model data into ModelInfo."""
        try:
            name = model_data.get("name", "")
            size_bytes = model_data.get("size", 0)
            size_gb = size_bytes / (1024**3) if size_bytes else 0

            # Determine model type from name
            model_type = ModelType.TEXT_GENERATION
            if "embed" in name.lower():
                model_type = ModelType.EMBEDDINGS
            elif "code" in name.lower():
                model_type = ModelType.CODE
            elif "vision" in name.lower():
                model_type = ModelType.VISION

            return ModelInfo(
                id=f"ollama:{name}",
                name=name,
                provider=ModelProvider.OLLAMA,
                model_type=model_type,
                description=f"Ollama model: {name}",
                size_gb=size_gb,
                parameters=None,  # Not provided by Ollama API
                architecture="Unknown",
                license="Custom",
                tags=["ollama"],
                download_url=f"ollama pull {name}",
                is_local=is_local,
                is_downloaded=is_local,
                provider_metadata={
                    "ollama_name": name,
                    "digest": model_data.get("digest", ""),
                    "modified_at": model_data.get("modified_at", "")
                }
            )

        except Exception as e:
            logger.error(f"Error parsing Ollama model data: {e}")
            return None

    async def _discover_huggingface_models(
        self,
        model_types: Optional[List[ModelType]] = None
    ) -> List[ModelInfo]:
        """Discover models from HuggingFace."""
        models = []
        
        # Popular HuggingFace models by category
        popular_models = {
            ModelType.TEXT_GENERATION: [
                "microsoft/DialoGPT-medium",
                "gpt2",
                "facebook/blenderbot-400M-distill",
                "microsoft/DialoGPT-small"
            ],
            ModelType.EMBEDDINGS: [
                "sentence-transformers/all-MiniLM-L6-v2",
                "sentence-transformers/all-mpnet-base-v2",
                "sentence-transformers/paraphrase-MiniLM-L6-v2",
                "sentence-transformers/distilbert-base-nli-stsb-mean-tokens"
            ],
            ModelType.VISION: [
                "google/vit-base-patch16-224",
                "microsoft/resnet-50",
                "facebook/detr-resnet-50"
            ],
            ModelType.CODE: [
                "microsoft/codebert-base",
                "Salesforce/codet5-small"
            ]
        }

        target_types = model_types or list(ModelType)
        
        for model_type in target_types:
            if model_type in popular_models:
                for model_name in popular_models[model_type]:
                    try:
                        model_info = await self._get_huggingface_model_info(model_name, model_type)
                        if model_info:
                            models.append(model_info)
                    except Exception as e:
                        logger.error(f"Error fetching HuggingFace model {model_name}: {e}")

        return models

    async def _get_huggingface_model_info(
        self,
        model_name: str,
        model_type: ModelType
    ) -> Optional[ModelInfo]:
        """Get detailed information about a HuggingFace model."""
        try:
            url = f"https://huggingface.co/api/models/{model_name}"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return self._parse_huggingface_model(data, model_type)
        except Exception as e:
            logger.error(f"Error fetching HuggingFace model info for {model_name}: {e}")
        
        return None

    def _parse_huggingface_model(
        self,
        model_data: Dict[str, Any],
        model_type: ModelType
    ) -> Optional[ModelInfo]:
        """Parse HuggingFace model data into ModelInfo."""
        try:
            model_id = model_data.get("modelId", "")
            
            # Estimate size based on model type and parameters
            size_gb = self._estimate_huggingface_model_size(model_data, model_type)
            
            # Extract tags and metadata
            tags = model_data.get("tags", [])
            pipeline_tag = model_data.get("pipeline_tag", "")
            
            # Get license
            license_info = "Unknown"
            if "license" in model_data:
                license_info = model_data["license"]
            
            # Get description
            description = model_data.get("description", f"HuggingFace {model_type.value} model")
            
            return ModelInfo(
                id=f"huggingface:{model_id}",
                name=model_id,
                provider=ModelProvider.HUGGINGFACE,
                model_type=model_type,
                description=description,
                size_gb=size_gb,
                parameters=None,  # Would need to parse config for this
                architecture=pipeline_tag or "Unknown",
                license=license_info,
                tags=["huggingface"] + tags,
                download_url=f"https://huggingface.co/{model_id}",
                homepage_url=f"https://huggingface.co/{model_id}",
                available_quantizations=[QuantizationType.FP32, QuantizationType.FP16],
                provider_metadata={
                    "pipeline_tag": pipeline_tag,
                    "downloads": model_data.get("downloads", 0),
                    "likes": model_data.get("likes", 0),
                    "created_at": model_data.get("createdAt", ""),
                    "last_modified": model_data.get("lastModified", "")
                }
            )

        except Exception as e:
            logger.error(f"Error parsing HuggingFace model data: {e}")
            return None

    def _estimate_huggingface_model_size(
        self,
        model_data: Dict[str, Any],
        model_type: ModelType
    ) -> float:
        """Estimate model size based on type and available metadata."""
        # Basic size estimates based on model type and name patterns
        model_name = model_data.get("modelId", "").lower()
        
        if "large" in model_name:
            return 1.2
        elif "medium" in model_name:
            return 0.8
        elif "small" in model_name or "mini" in model_name:
            return 0.3
        elif "base" in model_name:
            return 0.5
        
        # Default sizes by model type
        size_defaults = {
            ModelType.TEXT_GENERATION: 0.8,
            ModelType.EMBEDDINGS: 0.3,
            ModelType.VISION: 0.5,
            ModelType.CODE: 0.6,
            ModelType.AUDIO: 0.4,
            ModelType.MULTIMODAL: 1.0
        }
        
        return size_defaults.get(model_type, 0.5)

    async def _discover_github_models(self) -> List[ModelInfo]:
        """Discover models from GitHub repositories."""
        models = []
        
        # Popular GitHub repositories with AI models
        github_repos = [
            {
                "repo": "ggerganov/llama.cpp",
                "models": ["llama-7b-ggml", "llama-13b-ggml"],
                "model_type": ModelType.TEXT_GENERATION,
                "description": "Llama models optimized for CPU inference"
            },
            {
                "repo": "nomic-ai/gpt4all",
                "models": ["gpt4all-j", "gpt4all-lora"],
                "model_type": ModelType.TEXT_GENERATION,
                "description": "GPT4All models for local inference"
            }
        ]
        
        for repo_info in github_repos:
            for model_name in repo_info["models"]:
                model = ModelInfo(
                    id=f"github:{repo_info['repo']}/{model_name}",
                    name=model_name,
                    provider=ModelProvider.GITHUB,
                    model_type=repo_info["model_type"],
                    description=repo_info["description"],
                    size_gb=2.0,  # Estimated
                    parameters=None,
                    architecture="Unknown",
                    license="MIT",  # Common for GitHub repos
                    tags=["github", "open-source"],
                    download_url=f"https://github.com/{repo_info['repo']}",
                    homepage_url=f"https://github.com/{repo_info['repo']}",
                    provider_metadata={
                        "repository": repo_info["repo"],
                        "model_file": model_name
                    }
                )
                models.append(model)
        
        return models

    def _is_cache_valid(self, provider: ModelProvider) -> bool:
        """Check if cache is valid for a provider."""
        cache_key = provider.value
        if cache_key not in self.cache_expiry:
            return False
        return datetime.now() < self.cache_expiry[cache_key]

    def _update_cache(self, provider: ModelProvider, models: List[ModelInfo]) -> None:
        """Update cache with new model data."""
        cache_key = provider.value
        self.cache[cache_key] = models
        self.cache_expiry[cache_key] = datetime.now() + self.cache_duration

    async def search_models(
        self,
        query: str,
        providers: Optional[List[ModelProvider]] = None,
        model_types: Optional[List[ModelType]] = None,
        max_size_gb: Optional[float] = None
    ) -> List[ModelInfo]:
        """Search for models matching criteria."""
        all_models = await self.discover_all_models(providers, model_types)
        
        # Flatten results
        models = []
        for provider_models in all_models.values():
            models.extend(provider_models)
        
        # Apply filters
        filtered_models = []
        query_lower = query.lower() if query else ""
        
        for model in models:
            # Text search
            if query and not any(query_lower in field.lower() for field in [
                model.name, model.description, model.architecture
            ] + model.tags + model.use_cases):
                continue
            
            # Size filter
            if max_size_gb and model.size_gb > max_size_gb:
                continue
            
            filtered_models.append(model)
        
        return filtered_models
