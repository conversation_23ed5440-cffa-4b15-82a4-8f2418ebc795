{".class": "MypyFile", "_fullname": "sqlite3.dbapi2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Binary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Binary", "line": 241, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.memoryview"}}}, "Blob": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Blob", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Connection", "kind": "Gdef"}, "Cursor": {".class": "SymbolTableNode", "cross_ref": "sqlite3.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "DataError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DataError", "kind": "Gdef"}, "DatabaseError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DatabaseError", "kind": "Gdef"}, "Date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Date", "line": 228, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.date"}}, "DateFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.DateFromTicks", "name": "DateFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "DateFromTicks", "ret_type": "datetime.date", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Error": {".class": "SymbolTableNode", "cross_ref": "sqlite3.E<PERSON>r", "kind": "Gdef"}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.IntegrityError", "kind": "Gdef"}, "InterfaceError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InterfaceError", "kind": "Gdef"}, "InternalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InternalError", "kind": "Gdef"}, "LEGACY_TRANSACTION_CONTROL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.L<PERSON><PERSON>Y_TRANSACTION_CONTROL", "kind": "Gdef"}, "NotSupportedError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.NotSupportedError", "kind": "Gdef"}, "OperationalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.OperationalError", "kind": "Gdef"}, "PARSE_COLNAMES": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.PARSE_COLNAMES", "kind": "Gdef"}, "PARSE_DECLTYPES": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.PARSE_DECLTYPES", "kind": "Gdef"}, "PrepareProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlite3.PrepareProtocol", "kind": "Gdef"}, "ProgrammingError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.ProgrammingError", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Row", "kind": "Gdef"}, "SQLITE_ABORT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ABORT", "kind": "Gdef"}, "SQLITE_ABORT_ROLLBACK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ABORT_ROLLBACK", "kind": "Gdef"}, "SQLITE_ALTER_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ALTER_TABLE", "kind": "Gdef"}, "SQLITE_ANALYZE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ANALYZE", "kind": "Gdef"}, "SQLITE_ATTACH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ATTACH", "kind": "Gdef"}, "SQLITE_AUTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_AUTH", "kind": "Gdef"}, "SQLITE_AUTH_USER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_AUTH_USER", "kind": "Gdef"}, "SQLITE_BUSY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_BUSY", "kind": "Gdef"}, "SQLITE_BUSY_RECOVERY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_BUSY_RECOVERY", "kind": "Gdef"}, "SQLITE_BUSY_SNAPSHOT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_BUSY_SNAPSHOT", "kind": "Gdef"}, "SQLITE_BUSY_TIMEOUT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_BUSY_TIMEOUT", "kind": "Gdef"}, "SQLITE_CANTOPEN": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN", "kind": "Gdef"}, "SQLITE_CANTOPEN_CONVPATH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_CONVPATH", "kind": "Gdef"}, "SQLITE_CANTOPEN_DIRTYWAL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_DIRTYWAL", "kind": "Gdef"}, "SQLITE_CANTOPEN_FULLPATH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_FULLPATH", "kind": "Gdef"}, "SQLITE_CANTOPEN_ISDIR": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_ISDIR", "kind": "Gdef"}, "SQLITE_CANTOPEN_NOTEMPDIR": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_NOTEMPDIR", "kind": "Gdef"}, "SQLITE_CANTOPEN_SYMLINK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CANTOPEN_SYMLINK", "kind": "Gdef"}, "SQLITE_CONSTRAINT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT", "kind": "Gdef"}, "SQLITE_CONSTRAINT_CHECK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_CHECK", "kind": "Gdef"}, "SQLITE_CONSTRAINT_COMMITHOOK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_COMMITHOOK", "kind": "Gdef"}, "SQLITE_CONSTRAINT_FOREIGNKEY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_FOREIGNKEY", "kind": "Gdef"}, "SQLITE_CONSTRAINT_FUNCTION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_FUNCTION", "kind": "Gdef"}, "SQLITE_CONSTRAINT_NOTNULL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_NOTNULL", "kind": "Gdef"}, "SQLITE_CONSTRAINT_PINNED": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_PINNED", "kind": "Gdef"}, "SQLITE_CONSTRAINT_PRIMARYKEY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_PRIMARYKEY", "kind": "Gdef"}, "SQLITE_CONSTRAINT_ROWID": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_ROWID", "kind": "Gdef"}, "SQLITE_CONSTRAINT_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_TRIGGER", "kind": "Gdef"}, "SQLITE_CONSTRAINT_UNIQUE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_UNIQUE", "kind": "Gdef"}, "SQLITE_CONSTRAINT_VTAB": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CONSTRAINT_VTAB", "kind": "Gdef"}, "SQLITE_CORRUPT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CORRUPT", "kind": "Gdef"}, "SQLITE_CORRUPT_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CORRUPT_INDEX", "kind": "Gdef"}, "SQLITE_CORRUPT_SEQUENCE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CORRUPT_SEQUENCE", "kind": "Gdef"}, "SQLITE_CORRUPT_VTAB": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CORRUPT_VTAB", "kind": "Gdef"}, "SQLITE_CREATE_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_INDEX", "kind": "Gdef"}, "SQLITE_CREATE_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TABLE", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_INDEX", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_TABLE", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_TRIGGER", "kind": "Gdef"}, "SQLITE_CREATE_TEMP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TEMP_VIEW", "kind": "Gdef"}, "SQLITE_CREATE_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_TRIGGER", "kind": "Gdef"}, "SQLITE_CREATE_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_VIEW", "kind": "Gdef"}, "SQLITE_CREATE_VTABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_CREATE_VTABLE", "kind": "Gdef"}, "SQLITE_DBCONFIG_DEFENSIVE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_DEFENSIVE", "kind": "Gdef"}, "SQLITE_DBCONFIG_DQS_DDL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_DQS_DDL", "kind": "Gdef"}, "SQLITE_DBCONFIG_DQS_DML": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_DQS_DML", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_FKEY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_FKEY", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_QPSG": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_QPSG", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_TRIGGER", "kind": "Gdef"}, "SQLITE_DBCONFIG_ENABLE_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_ENABLE_VIEW", "kind": "Gdef"}, "SQLITE_DBCONFIG_LEGACY_ALTER_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_LEGACY_ALTER_TABLE", "kind": "Gdef"}, "SQLITE_DBCONFIG_LEGACY_FILE_FORMAT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_LEGACY_FILE_FORMAT", "kind": "Gdef"}, "SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE", "kind": "Gdef"}, "SQLITE_DBCONFIG_RESET_DATABASE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_RESET_DATABASE", "kind": "Gdef"}, "SQLITE_DBCONFIG_TRIGGER_EQP": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_TRIGGER_EQP", "kind": "Gdef"}, "SQLITE_DBCONFIG_TRUSTED_SCHEMA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_TRUSTED_SCHEMA", "kind": "Gdef"}, "SQLITE_DBCONFIG_WRITABLE_SCHEMA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DBCONFIG_WRITABLE_SCHEMA", "kind": "Gdef"}, "SQLITE_DELETE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DELETE", "kind": "Gdef"}, "SQLITE_DENY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DENY", "kind": "Gdef"}, "SQLITE_DETACH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DETACH", "kind": "Gdef"}, "SQLITE_DONE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DONE", "kind": "Gdef"}, "SQLITE_DROP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_INDEX", "kind": "Gdef"}, "SQLITE_DROP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TABLE", "kind": "Gdef"}, "SQLITE_DROP_TEMP_INDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_INDEX", "kind": "Gdef"}, "SQLITE_DROP_TEMP_TABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_TABLE", "kind": "Gdef"}, "SQLITE_DROP_TEMP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_TRIGGER", "kind": "Gdef"}, "SQLITE_DROP_TEMP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TEMP_VIEW", "kind": "Gdef"}, "SQLITE_DROP_TRIGGER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_TRIGGER", "kind": "Gdef"}, "SQLITE_DROP_VIEW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_VIEW", "kind": "Gdef"}, "SQLITE_DROP_VTABLE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_DROP_VTABLE", "kind": "Gdef"}, "SQLITE_EMPTY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_EMPTY", "kind": "Gdef"}, "SQLITE_ERROR": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ERROR", "kind": "Gdef"}, "SQLITE_ERROR_MISSING_COLLSEQ": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ERROR_MISSING_COLLSEQ", "kind": "Gdef"}, "SQLITE_ERROR_RETRY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ERROR_RETRY", "kind": "Gdef"}, "SQLITE_ERROR_SNAPSHOT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ERROR_SNAPSHOT", "kind": "Gdef"}, "SQLITE_FORMAT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_FORMAT", "kind": "Gdef"}, "SQLITE_FULL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_FULL", "kind": "Gdef"}, "SQLITE_FUNCTION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_FUNCTION", "kind": "Gdef"}, "SQLITE_IGNORE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IGNORE", "kind": "Gdef"}, "SQLITE_INSERT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_INSERT", "kind": "Gdef"}, "SQLITE_INTERNAL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_INTERNAL", "kind": "Gdef"}, "SQLITE_INTERRUPT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_INTERRUPT", "kind": "Gdef"}, "SQLITE_IOERR": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR", "kind": "Gdef"}, "SQLITE_IOERR_ACCESS": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_ACCESS", "kind": "Gdef"}, "SQLITE_IOERR_AUTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_AUTH", "kind": "Gdef"}, "SQLITE_IOERR_BEGIN_ATOMIC": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_BEGIN_ATOMIC", "kind": "Gdef"}, "SQLITE_IOERR_BLOCKED": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_BLOCKED", "kind": "Gdef"}, "SQLITE_IOERR_CHECKRESERVEDLOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_CHECKRESERVEDLOCK", "kind": "Gdef"}, "SQLITE_IOERR_CLOSE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_CLOSE", "kind": "Gdef"}, "SQLITE_IOERR_COMMIT_ATOMIC": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_COMMIT_ATOMIC", "kind": "Gdef"}, "SQLITE_IOERR_CONVPATH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_CONVPATH", "kind": "Gdef"}, "SQLITE_IOERR_CORRUPTFS": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_CORRUPTFS", "kind": "Gdef"}, "SQLITE_IOERR_DATA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_DATA", "kind": "Gdef"}, "SQLITE_IOERR_DELETE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_DELETE", "kind": "Gdef"}, "SQLITE_IOERR_DELETE_NOENT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_DELETE_NOENT", "kind": "Gdef"}, "SQLITE_IOERR_DIR_CLOSE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_DIR_CLOSE", "kind": "Gdef"}, "SQLITE_IOERR_DIR_FSYNC": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_DIR_FSYNC", "kind": "Gdef"}, "SQLITE_IOERR_FSTAT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_FSTAT", "kind": "Gdef"}, "SQLITE_IOERR_FSYNC": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_FSYNC", "kind": "Gdef"}, "SQLITE_IOERR_GETTEMPPATH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_GETTEMPPATH", "kind": "Gdef"}, "SQLITE_IOERR_LOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_LOCK", "kind": "Gdef"}, "SQLITE_IOERR_MMAP": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_MMAP", "kind": "Gdef"}, "SQLITE_IOERR_NOMEM": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_NOMEM", "kind": "Gdef"}, "SQLITE_IOERR_RDLOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_RDLOCK", "kind": "Gdef"}, "SQLITE_IOERR_READ": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_READ", "kind": "Gdef"}, "SQLITE_IOERR_ROLLBACK_ATOMIC": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_ROLLBACK_ATOMIC", "kind": "Gdef"}, "SQLITE_IOERR_SEEK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SEEK", "kind": "Gdef"}, "SQLITE_IOERR_SHMLOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SHMLOCK", "kind": "Gdef"}, "SQLITE_IOERR_SHMMAP": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SHMMAP", "kind": "Gdef"}, "SQLITE_IOERR_SHMOPEN": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SHMOPEN", "kind": "Gdef"}, "SQLITE_IOERR_SHMSIZE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SHMSIZE", "kind": "Gdef"}, "SQLITE_IOERR_SHORT_READ": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_SHORT_READ", "kind": "Gdef"}, "SQLITE_IOERR_TRUNCATE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_TRUNCATE", "kind": "Gdef"}, "SQLITE_IOERR_UNLOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_UNLOCK", "kind": "Gdef"}, "SQLITE_IOERR_VNODE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_VNODE", "kind": "Gdef"}, "SQLITE_IOERR_WRITE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_IOERR_WRITE", "kind": "Gdef"}, "SQLITE_LIMIT_ATTACHED": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_ATTACHED", "kind": "Gdef"}, "SQLITE_LIMIT_COLUMN": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_COLUMN", "kind": "Gdef"}, "SQLITE_LIMIT_COMPOUND_SELECT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_COMPOUND_SELECT", "kind": "Gdef"}, "SQLITE_LIMIT_EXPR_DEPTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_EXPR_DEPTH", "kind": "Gdef"}, "SQLITE_LIMIT_FUNCTION_ARG": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_FUNCTION_ARG", "kind": "Gdef"}, "SQLITE_LIMIT_LENGTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_LENGTH", "kind": "Gdef"}, "SQLITE_LIMIT_LIKE_PATTERN_LENGTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_LIKE_PATTERN_LENGTH", "kind": "Gdef"}, "SQLITE_LIMIT_SQL_LENGTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_SQL_LENGTH", "kind": "Gdef"}, "SQLITE_LIMIT_TRIGGER_DEPTH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_TRIGGER_DEPTH", "kind": "Gdef"}, "SQLITE_LIMIT_VARIABLE_NUMBER": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_VARIABLE_NUMBER", "kind": "Gdef"}, "SQLITE_LIMIT_VDBE_OP": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_VDBE_OP", "kind": "Gdef"}, "SQLITE_LIMIT_WORKER_THREADS": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LIMIT_WORKER_THREADS", "kind": "Gdef"}, "SQLITE_LOCKED": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LOCKED", "kind": "Gdef"}, "SQLITE_LOCKED_SHAREDCACHE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LOCKED_SHAREDCACHE", "kind": "Gdef"}, "SQLITE_LOCKED_VTAB": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_LOCKED_VTAB", "kind": "Gdef"}, "SQLITE_MISMATCH": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_MISMATCH", "kind": "Gdef"}, "SQLITE_MISUSE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_MISUSE", "kind": "Gdef"}, "SQLITE_NOLFS": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOLFS", "kind": "Gdef"}, "SQLITE_NOMEM": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOMEM", "kind": "Gdef"}, "SQLITE_NOTADB": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOTADB", "kind": "Gdef"}, "SQLITE_NOTFOUND": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOTFOUND", "kind": "Gdef"}, "SQLITE_NOTICE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOTICE", "kind": "Gdef"}, "SQLITE_NOTICE_RECOVER_ROLLBACK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOTICE_RECOVER_ROLLBACK", "kind": "Gdef"}, "SQLITE_NOTICE_RECOVER_WAL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_NOTICE_RECOVER_WAL", "kind": "Gdef"}, "SQLITE_OK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_OK", "kind": "Gdef"}, "SQLITE_OK_LOAD_PERMANENTLY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_OK_LOAD_PERMANENTLY", "kind": "Gdef"}, "SQLITE_OK_SYMLINK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_OK_SYMLINK", "kind": "Gdef"}, "SQLITE_PERM": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_PERM", "kind": "Gdef"}, "SQLITE_PRAGMA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_PRAGMA", "kind": "Gdef"}, "SQLITE_PROTOCOL": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_PROTOCOL", "kind": "Gdef"}, "SQLITE_RANGE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_RANGE", "kind": "Gdef"}, "SQLITE_READ": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READ", "kind": "Gdef"}, "SQLITE_READONLY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY", "kind": "Gdef"}, "SQLITE_READONLY_CANTINIT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_CANTINIT", "kind": "Gdef"}, "SQLITE_READONLY_CANTLOCK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_CANTLOCK", "kind": "Gdef"}, "SQLITE_READONLY_DBMOVED": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_DBMOVED", "kind": "Gdef"}, "SQLITE_READONLY_DIRECTORY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_DIRECTORY", "kind": "Gdef"}, "SQLITE_READONLY_RECOVERY": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_RECOVERY", "kind": "Gdef"}, "SQLITE_READONLY_ROLLBACK": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_READONLY_ROLLBACK", "kind": "Gdef"}, "SQLITE_RECURSIVE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_RECURSIVE", "kind": "Gdef"}, "SQLITE_REINDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_REINDEX", "kind": "Gdef"}, "SQLITE_ROW": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_ROW", "kind": "Gdef"}, "SQLITE_SAVEPOINT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_SAVEPOINT", "kind": "Gdef"}, "SQLITE_SCHEMA": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_SCHEMA", "kind": "Gdef"}, "SQLITE_SELECT": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_SELECT", "kind": "Gdef"}, "SQLITE_TOOBIG": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_TOOBIG", "kind": "Gdef"}, "SQLITE_TRANSACTION": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_TRANSACTION", "kind": "Gdef"}, "SQLITE_UPDATE": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_UPDATE", "kind": "Gdef"}, "SQLITE_WARNING": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_WARNING", "kind": "Gdef"}, "SQLITE_WARNING_AUTOINDEX": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.SQLITE_WARNING_AUTOINDEX", "kind": "Gdef"}, "Time": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Time", "line": 229, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.time"}}, "TimeFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.TimeFromTicks", "name": "TimeFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "TimeFromTicks", "ret_type": "datetime.time", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Timestamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlite3.dbapi2.Timestamp", "line": 230, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "datetime.datetime"}}, "TimestampFromTicks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ticks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlite3.dbapi2.TimestampFromTicks", "name": "TimestampFromTicks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ticks"], "arg_types": ["builtins.float"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "TimestampFromTicks", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Warning": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Warning", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "adapt": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.adapt", "kind": "Gdef"}, "adapters": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.adapters", "kind": "Gdef"}, "apilevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.apilevel", "name": "apilevel", "type": "builtins.str"}}, "complete_statement": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.complete_statement", "kind": "Gdef"}, "connect": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.connect", "kind": "Gdef"}, "converters": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.converters", "kind": "Gdef"}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef", "module_hidden": true, "module_public": false}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "enable_callback_tracebacks": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.enable_callback_tracebacks", "kind": "Gdef"}, "paramstyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.paramstyle", "name": "paramstyle", "type": "builtins.str"}}, "register_adapter": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.register_adapter", "kind": "Gdef"}, "register_converter": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.register_converter", "kind": "Gdef"}, "sqlite_version": {".class": "SymbolTableNode", "cross_ref": "_sqlite3.sqlite_version", "kind": "Gdef"}, "sqlite_version_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.sqlite_version_info", "name": "sqlite_version_info", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threadsafety": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.threadsafety", "name": "threadsafety", "type": "builtins.int"}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef", "module_hidden": true, "module_public": false}, "version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.version", "name": "version", "type": "builtins.str"}}, "version_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlite3.dbapi2.version_info", "name": "version_info", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\sqlite3\\dbapi2.pyi"}