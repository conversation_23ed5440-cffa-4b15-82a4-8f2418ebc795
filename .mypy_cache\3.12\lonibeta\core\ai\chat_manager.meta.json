{"data_mtime": 1757885706, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["asyncio", "datetime", "typing", "dataclasses", "enum", "uuid", "logging", "builtins", "_collections_abc", "_frozen_importlib", "abc", "types"], "hash": "bd4494235b4ab0a58b73c5c1f26c127f9a75055f", "id": "lonibeta.core.ai.chat_manager", "ignore_all": true, "interface_hash": "01d9ef6464a7ef4c113be8dc81fcd40952c761ce", "mtime": 1757884739, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\core\\ai\\chat_manager.py", "plugin_data": null, "size": 9384, "suppressed": [], "version_id": "1.15.0"}