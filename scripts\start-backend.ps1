# Start LoniBeta Backend

Write-Host "🐍 Starting LoniBeta Backend..." -ForegroundColor Green

# Check if we're in the right directory
if (-not (Test-Path "backend")) {
    Write-Host "❌ Please run this script from the project root directory" -ForegroundColor Red
    exit 1
}

Set-Location backend

# Activate virtual environment and start server
Write-Host "Starting FastAPI server..." -ForegroundColor Cyan
uv run uvicorn lonibeta.main:app --reload --host 0.0.0.0 --port 8000
