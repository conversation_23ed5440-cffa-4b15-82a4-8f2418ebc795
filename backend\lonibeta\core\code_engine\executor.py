"""
Code execution engine with security and sandboxing.
"""

import asyncio
import subprocess
import tempfile
import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import uuid
import logging

logger = logging.getLogger(__name__)


class Language(Enum):
    """Supported programming languages."""
    PYTHON = "python"
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    BASH = "bash"


class ExecutionStatus(Enum):
    """Code execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"


@dataclass
class ExecutionResult:
    """Code execution result."""
    id: str
    language: Language
    code: str
    status: ExecutionStatus
    output: str = ""
    error: str = ""
    exit_code: int = 0
    execution_time: float = 0.0
    memory_usage: int = 0  # in bytes
    created_at: datetime = None
    completed_at: Optional[datetime] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class CodeExecutor:
    """Secure code execution engine."""

    def __init__(self, timeout: int = 30, max_memory: int = 128 * 1024 * 1024):
        self.timeout = timeout
        self.max_memory = max_memory
        self.executions: Dict[str, ExecutionResult] = {}
        self.running_processes: Dict[str, subprocess.Popen] = {}

    async def execute_code(
        self,
        code: str,
        language: Language,
        timeout: Optional[int] = None,
        environment: Optional[Dict[str, str]] = None
    ) -> ExecutionResult:
        """Execute code in a secure environment."""
        execution_id = str(uuid.uuid4())
        timeout = timeout or self.timeout

        result = ExecutionResult(
            id=execution_id,
            language=language,
            code=code,
            status=ExecutionStatus.PENDING
        )

        self.executions[execution_id] = result

        try:
            result.status = ExecutionStatus.RUNNING
            start_time = datetime.now()

            # Execute based on language
            if language == Language.PYTHON:
                output, error, exit_code = await self._execute_python(code, timeout, environment)
            elif language == Language.JAVASCRIPT:
                output, error, exit_code = await self._execute_javascript(code, timeout, environment)
            elif language == Language.TYPESCRIPT:
                output, error, exit_code = await self._execute_typescript(code, timeout, environment)
            elif language == Language.BASH:
                output, error, exit_code = await self._execute_bash(code, timeout, environment)
            else:
                raise ValueError(f"Unsupported language: {language}")

            result.output = output
            result.error = error
            result.exit_code = exit_code
            result.status = ExecutionStatus.COMPLETED if exit_code == 0 else ExecutionStatus.FAILED
            result.completed_at = datetime.now()
            result.execution_time = (result.completed_at - start_time).total_seconds()

            logger.info(f"Code execution completed: {execution_id} ({language.value})")

        except asyncio.TimeoutError:
            result.status = ExecutionStatus.TIMEOUT
            result.error = f"Execution timed out after {timeout} seconds"
            logger.warning(f"Code execution timeout: {execution_id}")

        except Exception as e:
            result.status = ExecutionStatus.FAILED
            result.error = str(e)
            logger.error(f"Code execution error: {execution_id} - {e}")

        finally:
            # Clean up running process if exists
            if execution_id in self.running_processes:
                self.running_processes.pop(execution_id)

        return result

    async def _execute_python(
        self,
        code: str,
        timeout: int,
        environment: Optional[Dict[str, str]]
    ) -> Tuple[str, str, int]:
        """Execute Python code."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(code)
            temp_file = f.name

        try:
            cmd = ["python", temp_file]
            return await self._run_subprocess(cmd, timeout, environment)
        finally:
            os.unlink(temp_file)

    async def _execute_javascript(
        self,
        code: str,
        timeout: int,
        environment: Optional[Dict[str, str]]
    ) -> Tuple[str, str, int]:
        """Execute JavaScript code using Node.js."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.js', delete=False) as f:
            f.write(code)
            temp_file = f.name

        try:
            cmd = ["node", temp_file]
            return await self._run_subprocess(cmd, timeout, environment)
        finally:
            os.unlink(temp_file)

    async def _execute_typescript(
        self,
        code: str,
        timeout: int,
        environment: Optional[Dict[str, str]]
    ) -> Tuple[str, str, int]:
        """Execute TypeScript code."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.ts', delete=False) as f:
            f.write(code)
            temp_file = f.name

        try:
            # Compile TypeScript to JavaScript first
            js_file = temp_file.replace('.ts', '.js')
            compile_cmd = ["tsc", temp_file, "--outFile", js_file]
            
            # Compile
            compile_result = await self._run_subprocess(compile_cmd, 10, environment)
            if compile_result[2] != 0:  # Compilation failed
                return compile_result

            # Execute compiled JavaScript
            cmd = ["node", js_file]
            return await self._run_subprocess(cmd, timeout, environment)
        finally:
            for file in [temp_file, temp_file.replace('.ts', '.js')]:
                if os.path.exists(file):
                    os.unlink(file)

    async def _execute_bash(
        self,
        code: str,
        timeout: int,
        environment: Optional[Dict[str, str]]
    ) -> Tuple[str, str, int]:
        """Execute Bash script."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.sh', delete=False) as f:
            f.write(code)
            temp_file = f.name

        try:
            # Make script executable
            os.chmod(temp_file, 0o755)
            cmd = ["bash", temp_file]
            return await self._run_subprocess(cmd, timeout, environment)
        finally:
            os.unlink(temp_file)

    async def _run_subprocess(
        self,
        cmd: List[str],
        timeout: int,
        environment: Optional[Dict[str, str]]
    ) -> Tuple[str, str, int]:
        """Run subprocess with timeout and resource limits."""
        env = os.environ.copy()
        if environment:
            env.update(environment)

        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env=env,
                limit=self.max_memory
            )

            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                return (
                    stdout.decode('utf-8', errors='replace'),
                    stderr.decode('utf-8', errors='replace'),
                    process.returncode
                )

            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                raise

        except Exception as e:
            return "", str(e), 1

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel a running execution."""
        if execution_id not in self.executions:
            return False

        result = self.executions[execution_id]
        if result.status != ExecutionStatus.RUNNING:
            return False

        if execution_id in self.running_processes:
            process = self.running_processes[execution_id]
            process.kill()
            self.running_processes.pop(execution_id)

        result.status = ExecutionStatus.CANCELLED
        result.completed_at = datetime.now()
        
        logger.info(f"Cancelled execution: {execution_id}")
        return True

    async def get_execution(self, execution_id: str) -> Optional[ExecutionResult]:
        """Get execution result by ID."""
        return self.executions.get(execution_id)

    async def get_execution_history(
        self,
        language: Optional[Language] = None,
        limit: int = 100
    ) -> List[ExecutionResult]:
        """Get execution history."""
        executions = list(self.executions.values())
        
        if language:
            executions = [e for e in executions if e.language == language]

        # Sort by creation time (newest first)
        executions.sort(key=lambda x: x.created_at, reverse=True)
        
        return executions[:limit]

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics."""
        executions = list(self.executions.values())
        total_executions = len(executions)
        
        if total_executions == 0:
            return {
                "total_executions": 0,
                "success_rate": 0,
                "average_execution_time": 0,
                "language_breakdown": {}
            }

        successful = sum(1 for e in executions if e.status == ExecutionStatus.COMPLETED)
        total_time = sum(e.execution_time for e in executions if e.execution_time > 0)
        
        language_counts = {}
        for execution in executions:
            lang = execution.language.value
            language_counts[lang] = language_counts.get(lang, 0) + 1

        return {
            "total_executions": total_executions,
            "success_rate": successful / total_executions,
            "average_execution_time": total_time / total_executions,
            "language_breakdown": language_counts,
            "running_executions": sum(1 for e in executions if e.status == ExecutionStatus.RUNNING)
        }
