"""
AI & Machine Learning API endpoints.
"""

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status, UploadFile, File, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

from lonibeta.database import get_db
from lonibeta.core.ai.model_discovery import ModelDiscoveryService, ModelProvider, ModelType, ModelInfo
from lonibeta.core.ai.hardware_compatibility import HardwareCompatibilityEngine
from lonibeta.core.monitoring.system_monitor import SystemMonitor

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/chat")
async def chat_completion(
    message_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Process chat message with AI.
    """
    message = message_data.get("message")
    if not message:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Message is required"
        )
    
    # TODO: Implement actual AI chat processing
    return {
        "response": f"AI Response to: {message}",
        "model": "local-llm",
        "timestamp": datetime.now().isoformat() + "Z",
        "tokens_used": 150,
        "processing_time": 0.8
    }


@router.get("/models/discover")
async def discover_models(
    providers: Optional[List[str]] = Query(None, description="Providers to search (ollama, huggingface, github)"),
    model_types: Optional[List[str]] = Query(None, description="Model types to filter"),
    query: Optional[str] = Query(None, description="Search query"),
    max_size_gb: Optional[float] = Query(None, description="Maximum model size in GB"),
    include_compatibility: bool = Query(True, description="Include hardware compatibility analysis"),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Discover AI models from multiple providers with hardware compatibility analysis.
    """
    try:
        # Parse providers
        provider_list = None
        if providers:
            provider_list = [ModelProvider(p) for p in providers if p in [e.value for e in ModelProvider]]

        # Parse model types
        type_list = None
        if model_types:
            type_list = [ModelType(t) for t in model_types if t in [e.value for e in ModelType]]

        # Discover models
        async with ModelDiscoveryService() as discovery:
            if query:
                models = await discovery.search_models(
                    query=query,
                    providers=provider_list,
                    model_types=type_list,
                    max_size_gb=max_size_gb
                )
                results = {"all": models}
            else:
                results = await discovery.discover_all_models(
                    providers=provider_list,
                    model_types=type_list
                )

        # Convert to serializable format
        serialized_results = {}
        for provider, models in results.items():
            provider_key = provider.value if hasattr(provider, 'value') else str(provider)
            serialized_results[provider_key] = [
                {
                    "id": model.id,
                    "name": model.name,
                    "provider": model.provider.value,
                    "model_type": model.model_type.value,
                    "description": model.description,
                    "size_gb": model.size_gb,
                    "parameters": model.parameters,
                    "architecture": model.architecture,
                    "license": model.license,
                    "tags": model.tags,
                    "download_url": model.download_url,
                    "homepage_url": model.homepage_url,
                    "is_local": model.is_local,
                    "is_downloaded": model.is_downloaded,
                    "available_quantizations": [q.value for q in model.available_quantizations],
                    "context_length": model.context_length,
                    "languages": model.languages,
                    "use_cases": model.use_cases,
                    "created_at": model.created_at.isoformat() if model.created_at else None,
                    "last_checked": model.last_checked.isoformat() if model.last_checked else None
                }
                for model in models
            ]

        # Add hardware compatibility if requested
        if include_compatibility:
            system_monitor = SystemMonitor()
            hardware_specs = await system_monitor.get_hardware_specs()
            compatibility_engine = HardwareCompatibilityEngine()

            for provider_models in serialized_results.values():
                for model_data in provider_models:
                    # Reconstruct ModelInfo for compatibility analysis
                    model_info = ModelInfo(
                        id=model_data["id"],
                        name=model_data["name"],
                        provider=ModelProvider(model_data["provider"]),
                        model_type=ModelType(model_data["model_type"]),
                        description=model_data["description"],
                        size_gb=model_data["size_gb"],
                        parameters=model_data["parameters"],
                        architecture=model_data["architecture"],
                        license=model_data["license"],
                        tags=model_data["tags"],
                        download_url=model_data["download_url"],
                        homepage_url=model_data.get("homepage_url"),
                        paper_url=None
                    )

                    # Analyze compatibility
                    compatibility = await compatibility_engine.analyze_compatibility(
                        model_info, hardware_specs
                    )

                    # Add compatibility data
                    model_data["compatibility"] = {
                        "rating": compatibility.compatibility_rating.value,
                        "can_run": compatibility.can_run,
                        "optimal_quantization": compatibility.optimal_quantization.value,
                        "estimated_download_time_minutes": compatibility.estimated_download_time_minutes,
                        "recommendations": compatibility.recommendations,
                        "warnings": compatibility.warnings,
                        "alternative_models": compatibility.alternative_models
                    }

        return {
            "models": serialized_results,
            "total_count": sum(len(models) for models in serialized_results.values()),
            "providers_searched": list(serialized_results.keys()),
            "timestamp": datetime.now().isoformat() + "Z"
        }

    except Exception as e:
        logger.error(f"Error discovering models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to discover models: {str(e)}"
        )


@router.get("/models")
async def get_available_models(
    db: AsyncSession = Depends(get_db)
) -> List[Dict[str, Any]]:
    """
    Get list of available AI models (simplified version for backward compatibility).
    """
    try:
        async with ModelDiscoveryService() as discovery:
            results = await discovery.discover_all_models()

        # Flatten and simplify results
        models = []
        for provider_models in results.values():
            for model in provider_models:
                models.append({
                    "id": model.id,
                    "name": model.name,
                    "provider": model.provider.value,
                    "type": model.model_type.value,
                    "status": "downloaded" if model.is_downloaded else "available",
                    "size": f"{model.size_gb:.1f}GB"
                })

        return models

    except Exception as e:
        logger.error(f"Error getting models: {e}")
        # Fallback to static list
        return [
            {
                "id": "llama2-7b",
                "name": "Llama 2 7B",
                "provider": "ollama",
                "type": "text_generation",
                "status": "available",
                "size": "3.8GB"
            }
        ]


@router.get("/hardware/analysis")
async def get_hardware_analysis(
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get detailed hardware analysis for AI model compatibility.
    """
    try:
        system_monitor = SystemMonitor()
        hardware_specs = await system_monitor.get_hardware_specs()

        # Convert to serializable format
        return {
            "cpu": {
                "cores": hardware_specs.cpu_cores,
                "threads": hardware_specs.cpu_threads,
                "frequency_mhz": hardware_specs.cpu_frequency_mhz,
                "architecture": hardware_specs.cpu_architecture
            },
            "memory": {
                "total_gb": hardware_specs.memory_total_gb,
                "available_gb": hardware_specs.memory_available_gb,
                "usage_percent": ((hardware_specs.memory_total_gb - hardware_specs.memory_available_gb) / hardware_specs.memory_total_gb) * 100
            },
            "gpus": [
                {
                    "id": gpu.id,
                    "name": gpu.name,
                    "type": gpu.gpu_type.value,
                    "memory_total_mb": gpu.memory_total_mb,
                    "memory_used_mb": gpu.memory_used_mb,
                    "memory_free_mb": gpu.memory_free_mb,
                    "utilization_percent": gpu.utilization_percent,
                    "temperature_celsius": gpu.temperature_celsius,
                    "compute_capabilities": [cap.value for cap in gpu.compute_capabilities],
                    "driver_version": gpu.driver_version,
                    "cuda_version": gpu.cuda_version
                }
                for gpu in hardware_specs.gpus
            ],
            "platform": {
                "system": hardware_specs.platform_system,
                "release": hardware_specs.platform_release,
                "python_version": hardware_specs.python_version
            },
            "compute_capabilities": [cap.value for cap in hardware_specs.compute_capabilities],
            "ai_readiness": {
                "cpu_score": min(10, hardware_specs.cpu_cores * 2),
                "memory_score": min(10, hardware_specs.memory_total_gb / 3.2),
                "gpu_score": 10 if hardware_specs.gpus else 0,
                "overall_score": None  # Will be calculated
            },
            "timestamp": datetime.now().isoformat() + "Z"
        }

    except Exception as e:
        logger.error(f"Error analyzing hardware: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze hardware: {str(e)}"
        )


@router.post("/models/{model_id}/compatibility")
async def analyze_model_compatibility(
    model_id: str,
    analysis_options: Optional[Dict[str, Any]] = None,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze compatibility between a specific model and current hardware.
    """
    try:
        # Get hardware specs
        system_monitor = SystemMonitor()
        hardware_specs = await system_monitor.get_hardware_specs()

        # Find the model
        async with ModelDiscoveryService() as discovery:
            all_models = await discovery.discover_all_models()

        target_model = None
        for provider_models in all_models.values():
            for model in provider_models:
                if model.id == model_id:
                    target_model = model
                    break
            if target_model:
                break

        if not target_model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Model {model_id} not found"
            )

        # Analyze compatibility
        compatibility_engine = HardwareCompatibilityEngine()
        internet_speed = analysis_options.get("internet_speed_mbps", 100.0) if analysis_options else 100.0

        compatibility = await compatibility_engine.analyze_compatibility(
            target_model, hardware_specs, internet_speed
        )

        # Convert to serializable format
        return {
            "model_id": model_id,
            "model_name": target_model.name,
            "compatibility": {
                "rating": compatibility.compatibility_rating.value,
                "can_run": compatibility.can_run,
                "optimal_quantization": compatibility.optimal_quantization.value,
                "estimated_download_time_minutes": compatibility.estimated_download_time_minutes
            },
            "memory_requirements": {
                quant.value: {
                    "minimum_ram_gb": req.minimum_ram_gb,
                    "recommended_ram_gb": req.recommended_ram_gb,
                    "minimum_vram_gb": req.minimum_vram_gb,
                    "recommended_vram_gb": req.recommended_vram_gb
                }
                for quant, req in compatibility.memory_requirements.items()
            },
            "performance_estimates": {
                quant.value: {
                    "tokens_per_second": est.tokens_per_second,
                    "inference_latency_ms": est.inference_latency_ms,
                    "memory_usage_gb": est.memory_usage_gb,
                    "gpu_utilization_percent": est.gpu_utilization_percent,
                    "performance_level": est.performance_level.value,
                    "bottleneck": est.bottleneck
                }
                for quant, est in compatibility.performance_estimates.items()
            },
            "recommendations": compatibility.recommendations,
            "warnings": compatibility.warnings,
            "alternative_models": compatibility.alternative_models,
            "timestamp": datetime.now().isoformat() + "Z"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing model compatibility: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze compatibility: {str(e)}"
        )


@router.post("/models/download")
async def download_model(
    model_data: Dict[str, str],
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Download a model from Ollama, HuggingFace, or GitHub.
    """
    model_id = model_data.get("model_id")
    provider = model_data.get("provider", "ollama")

    if not model_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Model ID is required"
        )

    # TODO: Implement actual model download
    return {
        "model_id": model_id,
        "provider": provider,
        "status": "downloading",
        "progress": 0,
        "started_at": datetime.now().isoformat() + "Z"
    }


@router.get("/models/{model_id}/status")
async def get_model_status(
    model_id: str,
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Get download/installation status of a model.
    """
    # TODO: Implement actual status checking
    return {
        "model_id": model_id,
        "status": "ready",
        "progress": 100,
        "size": "3.8GB",
        "downloaded_at": "2025-09-14T20:00:00Z"
    }


@router.post("/voice/transcribe")
async def transcribe_audio(
    audio_file: UploadFile = File(...),
    language: Optional[str] = "en-US",
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Transcribe audio to text using speech recognition.
    """
    if not audio_file.content_type.startswith("audio/"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an audio file"
        )
    
    # TODO: Implement actual speech-to-text processing
    return {
        "transcription": "This is a placeholder transcription of the audio file.",
        "language": language,
        "confidence": 0.95,
        "duration": 5.2,
        "processing_time": 1.1,
        "timestamp": datetime.now().isoformat() + "Z"
    }


@router.post("/embeddings")
async def create_embeddings(
    text_data: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Create embeddings for text using sentence transformers.
    """
    texts = text_data.get("texts", [])
    model = text_data.get("model", "all-MiniLM-L6-v2")
    
    if not texts:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Texts array is required"
        )
    
    # TODO: Implement actual embedding generation
    return {
        "embeddings": [[0.1, 0.2, 0.3] * 128 for _ in texts],  # Placeholder
        "model": model,
        "dimension": 384,
        "processing_time": 0.3,
        "timestamp": datetime.now().isoformat() + "Z"
    }


@router.post("/analyze/behavior")
async def analyze_behavior_patterns(
    data: Dict[str, Any],
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """
    Analyze user behavior patterns using ML.
    """
    # TODO: Implement actual behavior analysis
    return {
        "patterns": [
            {
                "type": "usage_peak",
                "description": "High activity between 9-11 AM",
                "confidence": 0.87
            },
            {
                "type": "task_preference",
                "description": "Prefers automation tasks on weekdays",
                "confidence": 0.92
            }
        ],
        "recommendations": [
            "Schedule heavy tasks during low-activity periods",
            "Optimize UI for morning usage patterns"
        ],
        "analysis_date": datetime.now().isoformat() + "Z"
    }
