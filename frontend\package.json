{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.8", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.87.4", "autoprefixer": "^10.4.21", "axios": "^1.12.2", "date-fns": "^4.1.0", "lucide-react": "^0.544.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-router-dom": "^7.9.1", "recharts": "^3.2.0", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.13", "zod": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}