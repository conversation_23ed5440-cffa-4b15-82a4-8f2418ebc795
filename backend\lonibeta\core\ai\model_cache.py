"""
Caching system for AI model metadata and hardware analysis results.
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import aiofiles
import asyncio
import logging

from .model_discovery import ModelInfo, ModelProvider, ModelType
from .hardware_compatibility import CompatibilityAnalysis

logger = logging.getLogger(__name__)


class ModelCache:
    """Cache for AI model metadata and compatibility analysis."""

    def __init__(self, cache_dir: str = "./cache/models"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Cache configuration
        self.model_cache_duration = timedelta(hours=6)  # Model metadata cache
        self.compatibility_cache_duration = timedelta(hours=1)  # Hardware analysis cache
        self.hardware_cache_duration = timedelta(minutes=30)  # Hardware specs cache
        
        # Cache file paths
        self.models_cache_file = self.cache_dir / "models.json"
        self.compatibility_cache_file = self.cache_dir / "compatibility.json"
        self.hardware_cache_file = self.cache_dir / "hardware.json"
        
        # In-memory cache for frequently accessed data
        self.memory_cache: Dict[str, Any] = {}
        self.memory_cache_timestamps: Dict[str, datetime] = {}

    async def get_cached_models(
        self,
        providers: Optional[List[ModelProvider]] = None
    ) -> Optional[Dict[str, List[ModelInfo]]]:
        """Get cached model data if valid."""
        try:
            if not self.models_cache_file.exists():
                return None
            
            # Check file age
            file_age = datetime.now() - datetime.fromtimestamp(
                self.models_cache_file.stat().st_mtime
            )
            
            if file_age > self.model_cache_duration:
                logger.debug("Model cache expired")
                return None
            
            # Load cached data
            async with aiofiles.open(self.models_cache_file, 'r') as f:
                cache_data = json.loads(await f.read())
            
            # Convert back to ModelInfo objects
            cached_models = {}
            for provider_str, models_data in cache_data.items():
                try:
                    provider = ModelProvider(provider_str)
                    
                    # Filter by requested providers
                    if providers and provider not in providers:
                        continue
                    
                    models = []
                    for model_data in models_data:
                        model = self._deserialize_model(model_data)
                        if model:
                            models.append(model)
                    
                    cached_models[provider] = models
                    
                except ValueError:
                    logger.warning(f"Unknown provider in cache: {provider_str}")
                    continue
            
            logger.info(f"Loaded {sum(len(models) for models in cached_models.values())} models from cache")
            return cached_models
            
        except Exception as e:
            logger.error(f"Error loading model cache: {e}")
            return None

    async def cache_models(self, models: Dict[ModelProvider, List[ModelInfo]]) -> None:
        """Cache model data to disk."""
        try:
            # Convert to serializable format
            cache_data = {}
            for provider, model_list in models.items():
                cache_data[provider.value] = [
                    self._serialize_model(model) for model in model_list
                ]
            
            # Write to cache file
            async with aiofiles.open(self.models_cache_file, 'w') as f:
                await f.write(json.dumps(cache_data, indent=2))
            
            logger.info(f"Cached {sum(len(models) for models in models.values())} models")
            
        except Exception as e:
            logger.error(f"Error caching models: {e}")

    async def get_cached_compatibility(
        self,
        model_id: str,
        hardware_hash: str
    ) -> Optional[CompatibilityAnalysis]:
        """Get cached compatibility analysis."""
        try:
            cache_key = f"{model_id}:{hardware_hash}"
            
            # Check memory cache first
            if cache_key in self.memory_cache:
                cache_time = self.memory_cache_timestamps.get(cache_key)
                if cache_time and datetime.now() - cache_time < self.compatibility_cache_duration:
                    logger.debug(f"Found compatibility in memory cache: {model_id}")
                    return self.memory_cache[cache_key]
                else:
                    # Remove expired entry
                    self.memory_cache.pop(cache_key, None)
                    self.memory_cache_timestamps.pop(cache_key, None)
            
            # Check disk cache
            if not self.compatibility_cache_file.exists():
                return None
            
            async with aiofiles.open(self.compatibility_cache_file, 'r') as f:
                cache_data = json.loads(await f.read())
            
            if cache_key in cache_data:
                entry = cache_data[cache_key]
                
                # Check if entry is still valid
                cached_time = datetime.fromisoformat(entry["timestamp"])
                if datetime.now() - cached_time < self.compatibility_cache_duration:
                    compatibility = self._deserialize_compatibility(entry["data"])
                    
                    # Store in memory cache
                    self.memory_cache[cache_key] = compatibility
                    self.memory_cache_timestamps[cache_key] = datetime.now()
                    
                    logger.debug(f"Found compatibility in disk cache: {model_id}")
                    return compatibility
            
            return None
            
        except Exception as e:
            logger.error(f"Error loading compatibility cache: {e}")
            return None

    async def cache_compatibility(
        self,
        model_id: str,
        hardware_hash: str,
        compatibility: CompatibilityAnalysis
    ) -> None:
        """Cache compatibility analysis."""
        try:
            cache_key = f"{model_id}:{hardware_hash}"
            
            # Store in memory cache
            self.memory_cache[cache_key] = compatibility
            self.memory_cache_timestamps[cache_key] = datetime.now()
            
            # Load existing disk cache
            cache_data = {}
            if self.compatibility_cache_file.exists():
                async with aiofiles.open(self.compatibility_cache_file, 'r') as f:
                    cache_data = json.loads(await f.read())
            
            # Add new entry
            cache_data[cache_key] = {
                "timestamp": datetime.now().isoformat(),
                "data": self._serialize_compatibility(compatibility)
            }
            
            # Clean up old entries
            cutoff_time = datetime.now() - self.compatibility_cache_duration
            cache_data = {
                k: v for k, v in cache_data.items()
                if datetime.fromisoformat(v["timestamp"]) > cutoff_time
            }
            
            # Write back to disk
            async with aiofiles.open(self.compatibility_cache_file, 'w') as f:
                await f.write(json.dumps(cache_data, indent=2))
            
            logger.debug(f"Cached compatibility analysis: {model_id}")
            
        except Exception as e:
            logger.error(f"Error caching compatibility: {e}")

    async def get_cached_hardware_specs(self) -> Optional[Dict[str, Any]]:
        """Get cached hardware specifications."""
        try:
            if not self.hardware_cache_file.exists():
                return None
            
            # Check file age
            file_age = datetime.now() - datetime.fromtimestamp(
                self.hardware_cache_file.stat().st_mtime
            )
            
            if file_age > self.hardware_cache_duration:
                return None
            
            async with aiofiles.open(self.hardware_cache_file, 'r') as f:
                hardware_data = json.loads(await f.read())
            
            logger.debug("Loaded hardware specs from cache")
            return hardware_data
            
        except Exception as e:
            logger.error(f"Error loading hardware cache: {e}")
            return None

    async def cache_hardware_specs(self, hardware_data: Dict[str, Any]) -> None:
        """Cache hardware specifications."""
        try:
            async with aiofiles.open(self.hardware_cache_file, 'w') as f:
                await f.write(json.dumps(hardware_data, indent=2))
            
            logger.debug("Cached hardware specifications")
            
        except Exception as e:
            logger.error(f"Error caching hardware specs: {e}")

    def generate_hardware_hash(self, hardware_data: Dict[str, Any]) -> str:
        """Generate a hash for hardware configuration."""
        # Create a stable hash based on key hardware characteristics
        hash_data = {
            "cpu_cores": hardware_data.get("cpu", {}).get("cores"),
            "memory_total_gb": hardware_data.get("memory", {}).get("total_gb"),
            "gpus": [
                {
                    "name": gpu.get("name"),
                    "memory_total_mb": gpu.get("memory_total_mb"),
                    "type": gpu.get("type")
                }
                for gpu in hardware_data.get("gpus", [])
            ]
        }
        
        hash_string = json.dumps(hash_data, sort_keys=True)
        return hashlib.md5(hash_string.encode()).hexdigest()

    async def clear_cache(self, cache_type: Optional[str] = None) -> None:
        """Clear cache files."""
        try:
            if cache_type is None or cache_type == "models":
                if self.models_cache_file.exists():
                    self.models_cache_file.unlink()
                    logger.info("Cleared models cache")
            
            if cache_type is None or cache_type == "compatibility":
                if self.compatibility_cache_file.exists():
                    self.compatibility_cache_file.unlink()
                self.memory_cache.clear()
                self.memory_cache_timestamps.clear()
                logger.info("Cleared compatibility cache")
            
            if cache_type is None or cache_type == "hardware":
                if self.hardware_cache_file.exists():
                    self.hardware_cache_file.unlink()
                logger.info("Cleared hardware cache")
                
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        stats = {
            "models_cache": {
                "exists": self.models_cache_file.exists(),
                "size_bytes": 0,
                "age_hours": 0,
                "entries": 0
            },
            "compatibility_cache": {
                "exists": self.compatibility_cache_file.exists(),
                "size_bytes": 0,
                "age_hours": 0,
                "entries": 0,
                "memory_entries": len(self.memory_cache)
            },
            "hardware_cache": {
                "exists": self.hardware_cache_file.exists(),
                "size_bytes": 0,
                "age_hours": 0
            }
        }
        
        try:
            # Models cache stats
            if self.models_cache_file.exists():
                stat = self.models_cache_file.stat()
                stats["models_cache"]["size_bytes"] = stat.st_size
                age = datetime.now() - datetime.fromtimestamp(stat.st_mtime)
                stats["models_cache"]["age_hours"] = age.total_seconds() / 3600
                
                async with aiofiles.open(self.models_cache_file, 'r') as f:
                    data = json.loads(await f.read())
                    stats["models_cache"]["entries"] = sum(len(models) for models in data.values())
            
            # Compatibility cache stats
            if self.compatibility_cache_file.exists():
                stat = self.compatibility_cache_file.stat()
                stats["compatibility_cache"]["size_bytes"] = stat.st_size
                age = datetime.now() - datetime.fromtimestamp(stat.st_mtime)
                stats["compatibility_cache"]["age_hours"] = age.total_seconds() / 3600
                
                async with aiofiles.open(self.compatibility_cache_file, 'r') as f:
                    data = json.loads(await f.read())
                    stats["compatibility_cache"]["entries"] = len(data)
            
            # Hardware cache stats
            if self.hardware_cache_file.exists():
                stat = self.hardware_cache_file.stat()
                stats["hardware_cache"]["size_bytes"] = stat.st_size
                age = datetime.now() - datetime.fromtimestamp(stat.st_mtime)
                stats["hardware_cache"]["age_hours"] = age.total_seconds() / 3600
                
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
        
        return stats

    def _serialize_model(self, model: ModelInfo) -> Dict[str, Any]:
        """Serialize ModelInfo to dict."""
        return {
            "id": model.id,
            "name": model.name,
            "provider": model.provider.value,
            "model_type": model.model_type.value,
            "description": model.description,
            "size_gb": model.size_gb,
            "parameters": model.parameters,
            "architecture": model.architecture,
            "license": model.license,
            "tags": model.tags,
            "download_url": model.download_url,
            "homepage_url": model.homepage_url,
            "paper_url": model.paper_url,
            "is_local": model.is_local,
            "is_downloaded": model.is_downloaded,
            "download_progress": model.download_progress,
            "available_quantizations": [q.value for q in model.available_quantizations],
            "default_quantization": model.default_quantization.value,
            "context_length": model.context_length,
            "languages": model.languages,
            "use_cases": model.use_cases,
            "provider_metadata": model.provider_metadata,
            "created_at": model.created_at.isoformat() if model.created_at else None,
            "updated_at": model.updated_at.isoformat() if model.updated_at else None,
            "last_checked": model.last_checked.isoformat() if model.last_checked else None
        }

    def _deserialize_model(self, data: Dict[str, Any]) -> Optional[ModelInfo]:
        """Deserialize dict to ModelInfo."""
        try:
            from .model_discovery import QuantizationType
            
            return ModelInfo(
                id=data["id"],
                name=data["name"],
                provider=ModelProvider(data["provider"]),
                model_type=ModelType(data["model_type"]),
                description=data["description"],
                size_gb=data["size_gb"],
                parameters=data["parameters"],
                architecture=data["architecture"],
                license=data["license"],
                tags=data["tags"],
                download_url=data["download_url"],
                homepage_url=data.get("homepage_url"),
                paper_url=data.get("paper_url"),
                is_local=data.get("is_local", False),
                is_downloaded=data.get("is_downloaded", False),
                download_progress=data.get("download_progress", 0.0),
                available_quantizations=[QuantizationType(q) for q in data.get("available_quantizations", ["fp16"])],
                default_quantization=QuantizationType(data.get("default_quantization", "fp16")),
                context_length=data.get("context_length"),
                languages=data.get("languages", ["en"]),
                use_cases=data.get("use_cases", []),
                provider_metadata=data.get("provider_metadata", {}),
                created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
                updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
                last_checked=datetime.fromisoformat(data["last_checked"]) if data.get("last_checked") else None
            )
        except Exception as e:
            logger.error(f"Error deserializing model: {e}")
            return None

    def _serialize_compatibility(self, compatibility: CompatibilityAnalysis) -> Dict[str, Any]:
        """Serialize CompatibilityAnalysis to dict."""
        return {
            "model_id": compatibility.model_id,
            "compatibility_rating": compatibility.compatibility_rating.value,
            "can_run": compatibility.can_run,
            "optimal_quantization": compatibility.optimal_quantization.value,
            "estimated_download_time_minutes": compatibility.estimated_download_time_minutes,
            "recommendations": compatibility.recommendations,
            "warnings": compatibility.warnings,
            "alternative_models": compatibility.alternative_models
        }

    def _deserialize_compatibility(self, data: Dict[str, Any]) -> CompatibilityAnalysis:
        """Deserialize dict to CompatibilityAnalysis."""
        from .hardware_compatibility import CompatibilityRating
        from .model_discovery import QuantizationType
        
        return CompatibilityAnalysis(
            model_id=data["model_id"],
            compatibility_rating=CompatibilityRating(data["compatibility_rating"]),
            can_run=data["can_run"],
            memory_requirements={},  # Not cached for simplicity
            performance_estimates={},  # Not cached for simplicity
            recommendations=data["recommendations"],
            warnings=data["warnings"],
            alternative_models=data["alternative_models"],
            optimal_quantization=QuantizationType(data["optimal_quantization"]),
            estimated_download_time_minutes=data["estimated_download_time_minutes"]
        )
