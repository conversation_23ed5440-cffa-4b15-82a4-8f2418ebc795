import React, { useState } from 'react';
import { PlayIcon, CheckIcon, XMarkIcon } from '@heroicons/react/24/outline';

const extensions = [
  {
    id: 'python-linter',
    name: 'Python Linter',
    description: 'Advanced Python code analysis and linting',
    version: '1.2.0',
    status: 'installed',
    author: 'LoniBeta Team',
  },
  {
    id: 'js-formatter',
    name: 'JavaScript Formatter',
    description: 'JavaScript/TypeScript code formatting',
    version: '2.1.0',
    status: 'available',
    author: 'Community',
  },
  {
    id: 'security-scanner',
    name: 'Security Scanner',
    description: 'Scan code for security vulnerabilities',
    version: '1.0.5',
    status: 'installed',
    author: 'LoniBeta Team',
  },
];

export default function CodeEngine() {
  const [code, setCode] = useState(`def fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# Example usage
result = fibonacci(10)
print(f"The 10th Fibonacci number is: {result}")`);
  
  const [language, setLanguage] = useState('python');
  const [output, setOutput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);

  const handleExecute = async () => {
    setIsExecuting(true);
    // TODO: Implement actual code execution
    setTimeout(() => {
      setOutput('The 10th Fibonacci number is: 55\n');
      setIsExecuting(false);
    }, 2000);
  };

  const handleValidate = async () => {
    // TODO: Implement code validation
    console.log('Validating code...');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Code Engine</h1>
        <p className="mt-1 text-sm text-gray-500">
          Execute code, validate quality, and manage extensions
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Code Editor */}
        <div className="lg:col-span-2 space-y-4">
          {/* Language Selection */}
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Language</label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value)}
                className="input mt-1"
              >
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="typescript">TypeScript</option>
                <option value="bash">Bash</option>
              </select>
            </div>
            <div className="flex space-x-2 mt-6">
              <button
                onClick={handleExecute}
                disabled={isExecuting}
                className="btn-primary flex items-center space-x-2"
              >
                <PlayIcon className="h-4 w-4" />
                <span>{isExecuting ? 'Executing...' : 'Execute'}</span>
              </button>
              <button
                onClick={handleValidate}
                className="btn-outline flex items-center space-x-2"
              >
                <CheckIcon className="h-4 w-4" />
                <span>Validate</span>
              </button>
            </div>
          </div>

          {/* Code Editor */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Code Editor</h3>
            </div>
            <div className="p-4">
              <textarea
                value={code}
                onChange={(e) => setCode(e.target.value)}
                className="w-full h-64 font-mono text-sm border border-gray-300 rounded-md p-3 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter your code here..."
              />
            </div>
          </div>

          {/* Output */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Output</h3>
            </div>
            <div className="p-4">
              <pre className="w-full h-32 font-mono text-sm bg-gray-50 border border-gray-300 rounded-md p-3 overflow-auto">
                {output || 'No output yet. Run your code to see results.'}
              </pre>
            </div>
          </div>

          {/* Quality Report */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Code Quality Report</h3>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 sm:grid-cols-5 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">9.0</div>
                  <div className="text-sm text-gray-500">SOLID</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">8.0</div>
                  <div className="text-sm text-gray-500">SoC</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">9.5</div>
                  <div className="text-sm text-gray-500">DRY</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">8.0</div>
                  <div className="text-sm text-gray-500">YAGNI</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">7.5</div>
                  <div className="text-sm text-gray-500">KISS</div>
                </div>
              </div>
              <div className="mt-4">
                <div className="text-sm text-gray-600">
                  <p className="font-medium">Suggestions:</p>
                  <ul className="mt-2 space-y-1 list-disc list-inside">
                    <li>Consider using memoization for the Fibonacci function to improve performance</li>
                    <li>Add input validation for negative numbers</li>
                    <li>Consider iterative approach for better space complexity</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Extensions Panel */}
        <div className="space-y-6">
          {/* Extensions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Extensions</h3>
            </div>
            <div className="p-4 space-y-3">
              {extensions.map((extension) => (
                <div key={extension.id} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{extension.name}</p>
                      <p className="text-xs text-gray-500 mt-1">{extension.description}</p>
                      <p className="text-xs text-gray-500">v{extension.version} by {extension.author}</p>
                    </div>
                    <div className="ml-3">
                      {extension.status === 'installed' ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckIcon className="h-3 w-3 mr-1" />
                          Installed
                        </span>
                      ) : (
                        <button className="text-xs bg-primary-600 text-white px-3 py-1 rounded-md hover:bg-primary-700">
                          Install
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Code Metrics */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Code Metrics</h3>
            </div>
            <div className="p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Lines of Code</span>
                <span className="text-sm font-medium">45</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Cyclomatic Complexity</span>
                <span className="text-sm font-medium">3</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Maintainability Index</span>
                <span className="text-sm font-medium">85.2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Technical Debt</span>
                <span className="text-sm font-medium">2 hours</span>
              </div>
            </div>
          </div>

          {/* Recent Executions */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-3 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Executions</h3>
            </div>
            <div className="p-4 space-y-3">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Python script</p>
                  <p className="text-xs text-gray-500">2 minutes ago • Success</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">JavaScript validation</p>
                  <p className="text-xs text-gray-500">15 minutes ago • Error</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">TypeScript format</p>
                  <p className="text-xs text-gray-500">1 hour ago • Success</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
