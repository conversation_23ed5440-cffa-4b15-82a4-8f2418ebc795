{"data_mtime": 1757885706, "dep_lines": [5, 1, 1, 1, 1, 6, 7, 8], "dep_prios": [5, 5, 30, 30, 30, 5, 5, 5], "dependencies": ["lonibeta.core.ai.chat_manager", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "f19ec7a0bba21ab30ae67c03e05e59a6018c78a6", "id": "lonibeta.core.ai", "ignore_all": true, "interface_hash": "e564315c42bfeaddf3de081356ff3d836e1977ac", "mtime": 1757884708, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\core\\ai\\__init__.py", "plugin_data": null, "size": 297, "suppressed": ["lonibeta.core.ai.model_manager", "lonibeta.core.ai.voice_processor", "lonibeta.core.ai.embedding_service"], "version_id": "1.15.0"}