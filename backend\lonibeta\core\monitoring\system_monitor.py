"""
System monitoring for real-time performance metrics.
"""

import asyncio
import psutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    network_packets_sent: int
    network_packets_recv: int
    process_count: int
    boot_time: datetime


@dataclass
class ProcessInfo:
    """Process information."""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    username: str
    create_time: datetime


class SystemMonitor:
    """Real-time system monitoring."""

    def __init__(self, collection_interval: int = 5):
        self.collection_interval = collection_interval
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1000  # Keep last 1000 data points
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None

    async def start_monitoring(self) -> None:
        """Start continuous system monitoring."""
        if self.is_monitoring:
            logger.warning("System monitoring is already running")
            return

        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started system monitoring")

    async def stop_monitoring(self) -> None:
        """Stop system monitoring."""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("Stopped system monitoring")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                metrics = await self.collect_metrics()
                self._add_metrics_to_history(metrics)
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)

    async def collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)

        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        disk_percent = (disk.used / disk.total) * 100

        # Network metrics
        network = psutil.net_io_counters()

        # Process count
        process_count = len(psutil.pids())

        # Boot time
        boot_time = datetime.fromtimestamp(psutil.boot_time())

        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=memory_used_gb,
            memory_total_gb=memory_total_gb,
            disk_percent=disk_percent,
            disk_used_gb=disk_used_gb,
            disk_total_gb=disk_total_gb,
            network_bytes_sent=network.bytes_sent,
            network_bytes_recv=network.bytes_recv,
            network_packets_sent=network.packets_sent,
            network_packets_recv=network.packets_recv,
            process_count=process_count,
            boot_time=boot_time
        )

    def _add_metrics_to_history(self, metrics: SystemMetrics) -> None:
        """Add metrics to history with size limit."""
        self.metrics_history.append(metrics)
        
        # Keep only the last max_history_size entries
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]

    async def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        return await self.collect_metrics()

    async def get_metrics_history(
        self,
        hours: int = 24,
        interval_minutes: int = 5
    ) -> List[SystemMetrics]:
        """Get historical metrics."""
        if not self.metrics_history:
            return []

        # Calculate how many data points we need
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter metrics within the time range
        filtered_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]

        # If interval is specified, sample the data
        if interval_minutes > 0:
            interval_seconds = interval_minutes * 60
            sampled_metrics = []
            last_timestamp = None

            for metric in filtered_metrics:
                if (last_timestamp is None or 
                    (metric.timestamp - last_timestamp).total_seconds() >= interval_seconds):
                    sampled_metrics.append(metric)
                    last_timestamp = metric.timestamp

            return sampled_metrics

        return filtered_metrics

    async def get_running_processes(
        self,
        limit: int = 50,
        sort_by: str = "cpu"
    ) -> List[ProcessInfo]:
        """Get list of running processes."""
        processes = []

        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 
                                       'memory_info', 'status', 'username', 'create_time']):
            try:
                pinfo = proc.info
                
                # Convert memory to MB
                memory_mb = pinfo['memory_info'].rss / (1024 * 1024) if pinfo['memory_info'] else 0
                
                # Convert create_time to datetime
                create_time = datetime.fromtimestamp(pinfo['create_time']) if pinfo['create_time'] else datetime.now()

                process_info = ProcessInfo(
                    pid=pinfo['pid'],
                    name=pinfo['name'] or 'Unknown',
                    cpu_percent=pinfo['cpu_percent'] or 0.0,
                    memory_percent=pinfo['memory_percent'] or 0.0,
                    memory_mb=memory_mb,
                    status=pinfo['status'] or 'unknown',
                    username=pinfo['username'] or 'unknown',
                    create_time=create_time
                )
                
                processes.append(process_info)

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # Process disappeared or access denied
                continue

        # Sort processes
        if sort_by == "cpu":
            processes.sort(key=lambda x: x.cpu_percent, reverse=True)
        elif sort_by == "memory":
            processes.sort(key=lambda x: x.memory_percent, reverse=True)
        elif sort_by == "name":
            processes.sort(key=lambda x: x.name.lower())

        return processes[:limit]

    async def get_system_info(self) -> Dict[str, Any]:
        """Get general system information."""
        # CPU info
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memory info
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk info
        disk_partitions = psutil.disk_partitions()
        disk_usage = {}
        for partition in disk_partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage[partition.device] = {
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": (usage.used / usage.total) * 100
                }
            except PermissionError:
                continue

        # Network interfaces
        network_interfaces = psutil.net_if_addrs()
        
        # Boot time and uptime
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        return {
            "cpu": {
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "max_frequency_mhz": cpu_freq.max if cpu_freq else None
            },
            "memory": {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "used_gb": memory.used / (1024**3),
                "percent": memory.percent
            },
            "swap": {
                "total_gb": swap.total / (1024**3),
                "used_gb": swap.used / (1024**3),
                "percent": swap.percent
            },
            "disk": disk_usage,
            "network_interfaces": list(network_interfaces.keys()),
            "boot_time": boot_time.isoformat(),
            "uptime_seconds": uptime.total_seconds(),
            "uptime_string": str(uptime).split('.')[0]  # Remove microseconds
        }

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring status and statistics."""
        return {
            "is_monitoring": self.is_monitoring,
            "collection_interval": self.collection_interval,
            "history_size": len(self.metrics_history),
            "max_history_size": self.max_history_size,
            "oldest_metric": self.metrics_history[0].timestamp.isoformat() if self.metrics_history else None,
            "newest_metric": self.metrics_history[-1].timestamp.isoformat() if self.metrics_history else None
        }
