"""
System monitoring for real-time performance metrics.
"""

import asyncio
import psutil
import platform
import subprocess
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class GPUType(Enum):
    """GPU types for compatibility."""
    NVIDIA = "nvidia"
    AMD = "amd"
    INTEL = "intel"
    APPLE = "apple"
    UNKNOWN = "unknown"


class ComputeCapability(Enum):
    """Compute capabilities."""
    CUDA = "cuda"
    OPENCL = "opencl"
    METAL = "metal"
    VULKAN = "vulkan"
    CPU_ONLY = "cpu_only"


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_gb: float
    memory_total_gb: float
    disk_percent: float
    disk_used_gb: float
    disk_total_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    network_packets_sent: int
    network_packets_recv: int
    process_count: int
    boot_time: datetime


@dataclass
class GPUInfo:
    """GPU information."""
    id: int
    name: str
    gpu_type: GPUType
    memory_total_mb: int
    memory_used_mb: int
    memory_free_mb: int
    utilization_percent: float
    temperature_celsius: Optional[float]
    power_draw_watts: Optional[float]
    compute_capabilities: List[ComputeCapability]
    driver_version: Optional[str]
    cuda_version: Optional[str]


@dataclass
class HardwareSpecs:
    """Complete hardware specifications."""
    cpu_cores: int
    cpu_threads: int
    cpu_frequency_mhz: float
    cpu_architecture: str
    memory_total_gb: float
    memory_available_gb: float
    gpus: List[GPUInfo]
    platform_system: str
    platform_release: str
    python_version: str
    compute_capabilities: List[ComputeCapability]


@dataclass
class ProcessInfo:
    """Process information."""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    username: str
    create_time: datetime


class SystemMonitor:
    """Real-time system monitoring."""

    def __init__(self, collection_interval: int = 5):
        self.collection_interval = collection_interval
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1000  # Keep last 1000 data points
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None

    async def start_monitoring(self) -> None:
        """Start continuous system monitoring."""
        if self.is_monitoring:
            logger.warning("System monitoring is already running")
            return

        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Started system monitoring")

    async def stop_monitoring(self) -> None:
        """Stop system monitoring."""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("Stopped system monitoring")

    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while self.is_monitoring:
            try:
                metrics = await self.collect_metrics()
                self._add_metrics_to_history(metrics)
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.collection_interval)

    async def collect_metrics(self) -> SystemMetrics:
        """Collect current system metrics."""
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)

        # Memory metrics
        memory = psutil.virtual_memory()
        memory_used_gb = memory.used / (1024**3)
        memory_total_gb = memory.total / (1024**3)

        # Disk metrics
        disk = psutil.disk_usage('/')
        disk_used_gb = disk.used / (1024**3)
        disk_total_gb = disk.total / (1024**3)
        disk_percent = (disk.used / disk.total) * 100

        # Network metrics
        network = psutil.net_io_counters()

        # Process count
        process_count = len(psutil.pids())

        # Boot time
        boot_time = datetime.fromtimestamp(psutil.boot_time())

        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_gb=memory_used_gb,
            memory_total_gb=memory_total_gb,
            disk_percent=disk_percent,
            disk_used_gb=disk_used_gb,
            disk_total_gb=disk_total_gb,
            network_bytes_sent=network.bytes_sent,
            network_bytes_recv=network.bytes_recv,
            network_packets_sent=network.packets_sent,
            network_packets_recv=network.packets_recv,
            process_count=process_count,
            boot_time=boot_time
        )

    def _add_metrics_to_history(self, metrics: SystemMetrics) -> None:
        """Add metrics to history with size limit."""
        self.metrics_history.append(metrics)
        
        # Keep only the last max_history_size entries
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size:]

    async def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        return await self.collect_metrics()

    async def get_metrics_history(
        self,
        hours: int = 24,
        interval_minutes: int = 5
    ) -> List[SystemMetrics]:
        """Get historical metrics."""
        if not self.metrics_history:
            return []

        # Calculate how many data points we need
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        # Filter metrics within the time range
        filtered_metrics = [
            m for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]

        # If interval is specified, sample the data
        if interval_minutes > 0:
            interval_seconds = interval_minutes * 60
            sampled_metrics = []
            last_timestamp = None

            for metric in filtered_metrics:
                if (last_timestamp is None or 
                    (metric.timestamp - last_timestamp).total_seconds() >= interval_seconds):
                    sampled_metrics.append(metric)
                    last_timestamp = metric.timestamp

            return sampled_metrics

        return filtered_metrics

    async def get_running_processes(
        self,
        limit: int = 50,
        sort_by: str = "cpu"
    ) -> List[ProcessInfo]:
        """Get list of running processes."""
        processes = []

        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 
                                       'memory_info', 'status', 'username', 'create_time']):
            try:
                pinfo = proc.info
                
                # Convert memory to MB
                memory_mb = pinfo['memory_info'].rss / (1024 * 1024) if pinfo['memory_info'] else 0
                
                # Convert create_time to datetime
                create_time = datetime.fromtimestamp(pinfo['create_time']) if pinfo['create_time'] else datetime.now()

                process_info = ProcessInfo(
                    pid=pinfo['pid'],
                    name=pinfo['name'] or 'Unknown',
                    cpu_percent=pinfo['cpu_percent'] or 0.0,
                    memory_percent=pinfo['memory_percent'] or 0.0,
                    memory_mb=memory_mb,
                    status=pinfo['status'] or 'unknown',
                    username=pinfo['username'] or 'unknown',
                    create_time=create_time
                )
                
                processes.append(process_info)

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                # Process disappeared or access denied
                continue

        # Sort processes
        if sort_by == "cpu":
            processes.sort(key=lambda x: x.cpu_percent, reverse=True)
        elif sort_by == "memory":
            processes.sort(key=lambda x: x.memory_percent, reverse=True)
        elif sort_by == "name":
            processes.sort(key=lambda x: x.name.lower())

        return processes[:limit]

    async def get_system_info(self) -> Dict[str, Any]:
        """Get general system information."""
        # CPU info
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()
        
        # Memory info
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk info
        disk_partitions = psutil.disk_partitions()
        disk_usage = {}
        for partition in disk_partitions:
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_usage[partition.device] = {
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": (usage.used / usage.total) * 100
                }
            except PermissionError:
                continue

        # Network interfaces
        network_interfaces = psutil.net_if_addrs()
        
        # Boot time and uptime
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        return {
            "cpu": {
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None,
                "max_frequency_mhz": cpu_freq.max if cpu_freq else None
            },
            "memory": {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "used_gb": memory.used / (1024**3),
                "percent": memory.percent
            },
            "swap": {
                "total_gb": swap.total / (1024**3),
                "used_gb": swap.used / (1024**3),
                "percent": swap.percent
            },
            "disk": disk_usage,
            "network_interfaces": list(network_interfaces.keys()),
            "boot_time": boot_time.isoformat(),
            "uptime_seconds": uptime.total_seconds(),
            "uptime_string": str(uptime).split('.')[0]  # Remove microseconds
        }

    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get monitoring status and statistics."""
        return {
            "is_monitoring": self.is_monitoring,
            "collection_interval": self.collection_interval,
            "history_size": len(self.metrics_history),
            "max_history_size": self.max_history_size,
            "oldest_metric": self.metrics_history[0].timestamp.isoformat() if self.metrics_history else None,
            "newest_metric": self.metrics_history[-1].timestamp.isoformat() if self.metrics_history else None
        }

    async def detect_gpus(self) -> List[GPUInfo]:
        """Detect available GPUs and their specifications."""
        gpus = []

        # Try NVIDIA GPUs first
        nvidia_gpus = await self._detect_nvidia_gpus()
        gpus.extend(nvidia_gpus)

        # Try AMD GPUs
        amd_gpus = await self._detect_amd_gpus()
        gpus.extend(amd_gpus)

        # Try Intel GPUs
        intel_gpus = await self._detect_intel_gpus()
        gpus.extend(intel_gpus)

        # Try Apple Silicon
        apple_gpus = await self._detect_apple_gpus()
        gpus.extend(apple_gpus)

        return gpus

    async def _detect_nvidia_gpus(self) -> List[GPUInfo]:
        """Detect NVIDIA GPUs using nvidia-smi."""
        gpus = []

        try:
            # Try to run nvidia-smi
            result = subprocess.run([
                "nvidia-smi", "--query-gpu=index,name,memory.total,memory.used,memory.free,utilization.gpu,temperature.gpu,power.draw,driver_version",
                "--format=csv,noheader,nounits"
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 9:
                            try:
                                gpu_info = GPUInfo(
                                    id=int(parts[0]),
                                    name=parts[1],
                                    gpu_type=GPUType.NVIDIA,
                                    memory_total_mb=int(parts[2]),
                                    memory_used_mb=int(parts[3]),
                                    memory_free_mb=int(parts[4]),
                                    utilization_percent=float(parts[5]),
                                    temperature_celsius=float(parts[6]) if parts[6] != '[Not Supported]' else None,
                                    power_draw_watts=float(parts[7]) if parts[7] != '[Not Supported]' else None,
                                    compute_capabilities=[ComputeCapability.CUDA],
                                    driver_version=parts[8],
                                    cuda_version=await self._get_cuda_version()
                                )
                                gpus.append(gpu_info)
                            except (ValueError, IndexError) as e:
                                logger.warning(f"Error parsing NVIDIA GPU info: {e}")

        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            logger.debug(f"NVIDIA GPU detection failed: {e}")

        return gpus

    async def _get_cuda_version(self) -> Optional[str]:
        """Get CUDA version."""
        try:
            result = subprocess.run(["nvcc", "--version"], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                # Parse CUDA version from output
                for line in result.stdout.split('\n'):
                    if 'release' in line.lower():
                        import re
                        match = re.search(r'release (\d+\.\d+)', line)
                        if match:
                            return match.group(1)
        except Exception:
            pass
        return None

    async def _detect_amd_gpus(self) -> List[GPUInfo]:
        """Detect AMD GPUs using rocm-smi or other tools."""
        gpus = []

        try:
            # Try rocm-smi for AMD GPUs
            result = subprocess.run(["rocm-smi", "--showid", "--showproductname", "--showmeminfo", "--showuse"],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # Parse rocm-smi output (simplified)
                lines = result.stdout.split('\n')
                gpu_id = 0
                for line in lines:
                    if 'GPU' in line and 'Memory' in line:
                        gpu_info = GPUInfo(
                            id=gpu_id,
                            name="AMD GPU",  # Would need more parsing for actual name
                            gpu_type=GPUType.AMD,
                            memory_total_mb=0,  # Would need parsing
                            memory_used_mb=0,
                            memory_free_mb=0,
                            utilization_percent=0.0,
                            temperature_celsius=None,
                            power_draw_watts=None,
                            compute_capabilities=[ComputeCapability.OPENCL],
                            driver_version=None,
                            cuda_version=None
                        )
                        gpus.append(gpu_info)
                        gpu_id += 1

        except Exception as e:
            logger.debug(f"AMD GPU detection failed: {e}")

        return gpus

    async def _detect_intel_gpus(self) -> List[GPUInfo]:
        """Detect Intel GPUs."""
        gpus: List[GPUInfo] = []

        # Intel GPU detection is more complex and platform-dependent
        # For now, we'll do basic detection
        try:
            if platform.system() == "Windows":
                # Use wmic on Windows
                result = subprocess.run([
                    "wmic", "path", "win32_VideoController", "get", "name,AdapterRAM"
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'Intel' in line:
                            gpu_info = GPUInfo(
                                id=len(gpus),
                                name=line.strip(),
                                gpu_type=GPUType.INTEL,
                                memory_total_mb=0,  # Would need parsing
                                memory_used_mb=0,
                                memory_free_mb=0,
                                utilization_percent=0.0,
                                temperature_celsius=None,
                                power_draw_watts=None,
                                compute_capabilities=[ComputeCapability.OPENCL],
                                driver_version=None,
                                cuda_version=None
                            )
                            gpus.append(gpu_info)

        except Exception as e:
            logger.debug(f"Intel GPU detection failed: {e}")

        return gpus

    async def _detect_apple_gpus(self) -> List[GPUInfo]:
        """Detect Apple Silicon GPUs."""
        gpus: List[GPUInfo] = []

        if platform.system() == "Darwin":
            try:
                # Use system_profiler on macOS
                result = subprocess.run([
                    "system_profiler", "SPDisplaysDataType", "-json"
                ], capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    data = json.loads(result.stdout)
                    displays = data.get("SPDisplaysDataType", [])

                    for display in displays:
                        if "Apple" in display.get("sppci_model", ""):
                            gpu_info = GPUInfo(
                                id=len(gpus),
                                name=display.get("sppci_model", "Apple GPU"),
                                gpu_type=GPUType.APPLE,
                                memory_total_mb=0,  # Shared memory on Apple Silicon
                                memory_used_mb=0,
                                memory_free_mb=0,
                                utilization_percent=0.0,
                                temperature_celsius=None,
                                power_draw_watts=None,
                                compute_capabilities=[ComputeCapability.METAL],
                                driver_version=None,
                                cuda_version=None
                            )
                            gpus.append(gpu_info)

            except Exception as e:
                logger.debug(f"Apple GPU detection failed: {e}")

        return gpus

    async def get_hardware_specs(self) -> HardwareSpecs:
        """Get complete hardware specifications."""
        # CPU information
        cpu_count = psutil.cpu_count(logical=False) or 1
        cpu_threads = psutil.cpu_count(logical=True) or 1
        cpu_freq = psutil.cpu_freq()
        cpu_frequency = cpu_freq.current if cpu_freq else 0.0

        # Memory information
        memory = psutil.virtual_memory()
        memory_total_gb = memory.total / (1024**3)
        memory_available_gb = memory.available / (1024**3)

        # GPU information
        gpus = await self.detect_gpus()

        # Platform information
        platform_system = platform.system()
        platform_release = platform.release()
        python_version = platform.python_version()
        cpu_architecture = platform.machine()

        # Determine available compute capabilities
        compute_capabilities = [ComputeCapability.CPU_ONLY]
        for gpu in gpus:
            compute_capabilities.extend(gpu.compute_capabilities)

        # Remove duplicates
        compute_capabilities = list(set(compute_capabilities))

        return HardwareSpecs(
            cpu_cores=cpu_count,
            cpu_threads=cpu_threads,
            cpu_frequency_mhz=cpu_frequency,
            cpu_architecture=cpu_architecture,
            memory_total_gb=memory_total_gb,
            memory_available_gb=memory_available_gb,
            gpus=gpus,
            platform_system=platform_system,
            platform_release=platform_release,
            python_version=python_version,
            compute_capabilities=compute_capabilities
        )
