{"data_mtime": 1757885890, "dep_lines": [5, 7, 8, 9, 10, 11, 12, 13, 14, 420, 1, 1, 1, 1, 1, 1, 1, 6], "dep_prios": [10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["asyncio", "platform", "subprocess", "json", "datetime", "typing", "dataclasses", "enum", "logging", "re", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "abc", "asyncio.exceptions", "types"], "hash": "61d0705c30e72f0ed4f4a88feb507b692f9730c6", "id": "lonibeta.core.monitoring.system_monitor", "ignore_all": true, "interface_hash": "72d764f901c7427cf39c2cdcf099c569aedd8211", "mtime": 1757885815, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\core\\monitoring\\system_monitor.py", "plugin_data": null, "size": 21127, "suppressed": ["psutil"], "version_id": "1.15.0"}