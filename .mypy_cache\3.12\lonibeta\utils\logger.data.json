{".class": "MypyFile", "_fullname": "lonibeta.utils.logger", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RotatingFileHandler": {".class": "SymbolTableNode", "cross_ref": "logging.handlers.RotatingFileHandler", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.utils.logger.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "lonibeta.config.settings", "kind": "Gdef"}, "setup_logging": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.utils.logger.setup_logging", "name": "setup_logging", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "setup_logging", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\utils\\logger.py"}