# LoniBeta Development Guide

This guide covers the development setup, architecture, and workflows for the LoniBeta AI-Powered System Automation Platform.

## Quick Start

1. **Clone and setup**:
   ```powershell
   git clone <repository-url>
   cd LoniBeta
   .\scripts\dev.ps1
   ```

2. **Start development servers**:
   ```powershell
   .\scripts\start-all.ps1
   ```

3. **Access the application**:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

## Architecture Overview

### Backend (Python + FastAPI)
- **Framework**: FastAPI with async/await support
- **Database**: SQLite (development) / PostgreSQL (production)
- **Package Manager**: uv for fast dependency management
- **Key Features**:
  - RESTful API with automatic OpenAPI documentation
  - Real-time WebSocket support for monitoring
  - Modular architecture with core business logic separation
  - Comprehensive logging and error handling

### Frontend (React + TypeScript)
- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite for fast development and building
- **Package Manager**: bun for ultra-fast package management
- **Styling**: Tailwind CSS with custom design system
- **Key Features**:
  - Modern React with hooks and functional components
  - Real-time updates via WebSocket connections
  - Responsive design with mobile support
  - Type-safe development with TypeScript

## Project Structure

```
LoniBeta/
├── backend/                 # Python FastAPI backend
│   ├── lonibeta/
│   │   ├── api/            # API route handlers
│   │   ├── core/           # Business logic modules
│   │   │   ├── automation/ # Task automation
│   │   │   ├── ai/         # AI/ML processing
│   │   │   ├── code_engine/# Code execution
│   │   │   ├── monitoring/ # System monitoring
│   │   │   └── security/   # Security & data
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   ├── services/       # External integrations
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   └── pyproject.toml      # Python dependencies
├── frontend/               # React TypeScript frontend
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API service functions
│   │   ├── types/          # TypeScript definitions
│   │   └── utils/          # Utility functions
│   └── package.json        # Node.js dependencies
├── scripts/                # Development scripts
├── docs/                   # Documentation
└── README.md              # Project overview
```

## Development Workflow

### Backend Development

1. **Setup environment**:
   ```powershell
   cd backend
   uv sync
   ```

2. **Run development server**:
   ```powershell
   uv run uvicorn lonibeta.main:app --reload
   ```

3. **Code quality**:
   ```powershell
   uv run black lonibeta tests
   uv run isort lonibeta tests
   uv run flake8 lonibeta tests
   uv run mypy lonibeta
   ```

4. **Run tests**:
   ```powershell
   uv run pytest tests/ -v --cov=lonibeta
   ```

### Frontend Development

1. **Setup environment**:
   ```powershell
   cd frontend
   bun install
   ```

2. **Run development server**:
   ```powershell
   bun run dev
   ```

3. **Code quality**:
   ```powershell
   bun run lint
   bun run type-check
   ```

4. **Build for production**:
   ```powershell
   bun run build
   ```

## Core Modules

### Automation Engine
- **Location**: `backend/lonibeta/core/automation/`
- **Purpose**: Task scheduling, execution, and management
- **Key Components**:
  - `TaskManager`: Lifecycle management of automation tasks
  - `TaskScheduler`: Cron-based task scheduling
  - `TaskExecutor`: Secure task execution environment

### AI & ML Engine
- **Location**: `backend/lonibeta/core/ai/`
- **Purpose**: AI model management and processing
- **Key Components**:
  - `ChatManager`: AI chat sessions and conversations
  - `ModelManager`: Download and manage AI models
  - `VoiceProcessor`: Speech-to-text processing
  - `EmbeddingService`: Text embedding generation

### Code Engine
- **Location**: `backend/lonibeta/core/code_engine/`
- **Purpose**: Code execution and quality validation
- **Key Components**:
  - `CodeExecutor`: Secure code execution in sandboxed environment
  - `CodeValidator`: SOLID principles validation
  - `QualityAnalyzer`: Code complexity and maintainability analysis
  - `ExtensionManager`: Code extension management

### Monitoring System
- **Location**: `backend/lonibeta/core/monitoring/`
- **Purpose**: Real-time system performance monitoring
- **Key Components**:
  - `SystemMonitor`: CPU, memory, disk, network monitoring
  - `PerformanceTracker`: Historical performance data
  - `AlertManager`: Threshold-based alerting
  - `MetricsCollector`: Data collection and aggregation

### Security & Data
- **Location**: `backend/lonibeta/core/security/`
- **Purpose**: Privacy-focused data management and security
- **Key Components**:
  - `DataManager`: Local data storage and retrieval
  - `AuthManager`: Authentication and authorization
  - `BackupService`: Data backup and recovery
  - `EncryptionService`: Data encryption and security

## API Design

### RESTful Endpoints
- `/api/v1/auth/*` - Authentication and user management
- `/api/v1/automation/*` - Task automation management
- `/api/v1/ai/*` - AI/ML features and model management
- `/api/v1/code/*` - Code execution and validation
- `/api/v1/monitoring/*` - System monitoring and metrics

### WebSocket Endpoints
- `/api/v1/monitoring/realtime` - Real-time system metrics
- `/api/v1/ai/chat/stream` - Streaming AI chat responses

## Testing Strategy

### Backend Testing
- **Unit Tests**: Test individual functions and classes
- **Integration Tests**: Test API endpoints and database interactions
- **Performance Tests**: Test system performance under load
- **Security Tests**: Test authentication and authorization

### Frontend Testing
- **Component Tests**: Test React components in isolation
- **Integration Tests**: Test user workflows and API integration
- **E2E Tests**: Test complete user journeys
- **Accessibility Tests**: Ensure WCAG compliance

## Deployment

### Development
```powershell
.\scripts\start-all.ps1
```

### Production Build
```powershell
.\scripts\build.ps1
```

### Production Deployment
1. Run production build
2. Copy build directory to production server
3. Install Python and uv on production server
4. Run production start script

## Contributing

1. **Code Style**: Follow existing patterns and use provided linting tools
2. **Testing**: Write tests for new features and bug fixes
3. **Documentation**: Update documentation for API changes
4. **Security**: Follow security best practices for data handling
5. **Performance**: Consider performance implications of changes

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change ports in configuration files
2. **Permission errors**: Run scripts as administrator if needed
3. **Package installation failures**: Clear package caches and retry
4. **Database connection issues**: Check database configuration

### Getting Help

1. Check the logs in the `logs/` directory
2. Review API documentation at `/docs`
3. Check GitHub issues for known problems
4. Contact the development team

## Performance Considerations

- Use async/await for I/O operations
- Implement proper caching strategies
- Monitor memory usage in long-running tasks
- Use connection pooling for database operations
- Optimize frontend bundle size and loading times
