# Run tests for LoniBeta

Write-Host "🧪 Running LoniBeta Tests..." -ForegroundColor Green

# Backend tests
Write-Host "🐍 Running backend tests..." -ForegroundColor Yellow
Set-Location backend

if (Test-Path "tests") {
    Write-Host "Running Python tests with pytest..." -ForegroundColor Cyan
    uv run pytest tests/ -v --cov=lonibeta --cov-report=html --cov-report=term
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Backend tests passed" -ForegroundColor Green
    } else {
        Write-Host "❌ Backend tests failed" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️  No backend tests found" -ForegroundColor Yellow
}

Set-Location ..

# Frontend tests
Write-Host "⚛️  Running frontend tests..." -ForegroundColor Yellow
Set-Location frontend

Write-Host "Running TypeScript type checking..." -ForegroundColor Cyan
bun run type-check

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ TypeScript type checking passed" -ForegroundColor Green
} else {
    Write-Host "❌ TypeScript type checking failed" -ForegroundColor Red
}

Write-Host "Running ESLint..." -ForegroundColor Cyan
bun run lint

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ ESLint passed" -ForegroundColor Green
} else {
    Write-Host "❌ ESLint failed" -ForegroundColor Red
}

Set-Location ..

Write-Host "🎉 Test run complete!" -ForegroundColor Green
