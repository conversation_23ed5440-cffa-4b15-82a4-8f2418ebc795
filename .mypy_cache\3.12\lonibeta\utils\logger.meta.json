{"data_mtime": 1757884904, "dep_lines": [8, 10, 5, 6, 7, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["logging.handlers", "lonibeta.config", "logging", "sys", "pathlib", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "types", "typing"], "hash": "3e0f8d78997e9c497b441011e17226046165755c", "id": "lonibeta.utils.logger", "ignore_all": false, "interface_hash": "7fbd2e7af2473069fc6bb2e6da27d9236e732e5c", "mtime": 1757884903, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\utils\\logger.py", "plugin_data": null, "size": 1552, "suppressed": [], "version_id": "1.15.0"}