# LoniBeta API Documentation

This document provides detailed information about the LoniBeta API endpoints, request/response formats, and usage examples.

## Base URL

- **Development**: `http://localhost:8000`
- **Production**: `https://your-domain.com`

## Authentication

LoniBeta uses JWT-based authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## API Endpoints

### Authentication

#### POST /api/v1/auth/login
Login with username and password.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

**Response:**
```json
{
  "access_token": "string",
  "token_type": "bearer",
  "expires_in": 1800,
  "user": {
    "id": 1,
    "username": "string",
    "email": "string"
  }
}
```

#### POST /api/v1/auth/logout
Logout and invalidate token.

#### GET /api/v1/auth/me
Get current user information.

### Automation

#### GET /api/v1/automation/tasks
Get all automation tasks.

**Response:**
```json
[
  {
    "id": 1,
    "name": "Email Organization",
    "description": "Organize emails by sender and date",
    "status": "active",
    "schedule": "0 9 * * *",
    "last_run": "2025-09-14T09:00:00Z",
    "next_run": "2025-09-15T09:00:00Z",
    "created_at": "2025-09-14T08:00:00Z"
  }
]
```

#### POST /api/v1/automation/tasks
Create a new automation task.

**Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "schedule": "0 9 * * *",
  "config": {
    "source_directory": "/path/to/source",
    "target_directory": "/path/to/target"
  }
}
```

#### GET /api/v1/automation/tasks/{task_id}
Get a specific automation task.

#### PUT /api/v1/automation/tasks/{task_id}
Update an automation task.

#### DELETE /api/v1/automation/tasks/{task_id}
Delete an automation task.

#### POST /api/v1/automation/tasks/{task_id}/run
Manually run an automation task.

### AI & Machine Learning

#### POST /api/v1/ai/chat
Process chat message with AI.

**Request Body:**
```json
{
  "message": "Hello, can you help me organize my files?",
  "session_id": "optional-session-id",
  "model": "llama2-7b"
}
```

**Response:**
```json
{
  "response": "I'd be happy to help you organize your files...",
  "model": "llama2-7b",
  "timestamp": "2025-09-14T23:07:00Z",
  "tokens_used": 150,
  "processing_time": 0.8
}
```

#### GET /api/v1/ai/models
Get list of available AI models.

**Response:**
```json
[
  {
    "id": "llama2-7b",
    "name": "Llama 2 7B",
    "provider": "ollama",
    "type": "chat",
    "status": "available",
    "size": "3.8GB"
  }
]
```

#### POST /api/v1/ai/models/download
Download a model from Ollama, HuggingFace, or GitHub.

**Request Body:**
```json
{
  "model_id": "llama2:7b",
  "provider": "ollama"
}
```

#### POST /api/v1/ai/voice/transcribe
Transcribe audio to text.

**Request:** Multipart form data with audio file

**Response:**
```json
{
  "transcription": "This is the transcribed text",
  "language": "en-US",
  "confidence": 0.95,
  "duration": 5.2,
  "processing_time": 1.1
}
```

#### POST /api/v1/ai/embeddings
Create embeddings for text.

**Request Body:**
```json
{
  "texts": ["Hello world", "How are you?"],
  "model": "all-MiniLM-L6-v2"
}
```

### Code Engine

#### POST /api/v1/code/execute
Execute code in a secure environment.

**Request Body:**
```json
{
  "code": "print('Hello, World!')",
  "language": "python",
  "timeout": 30
}
```

**Response:**
```json
{
  "output": "Hello, World!\n",
  "error": null,
  "exit_code": 0,
  "execution_time": 0.05,
  "language": "python",
  "timestamp": "2025-09-14T23:07:00Z"
}
```

#### POST /api/v1/code/validate
Validate code against SOLID, SoC, DRY, YAGNI, and KISS principles.

**Request Body:**
```json
{
  "code": "def fibonacci(n): ...",
  "language": "python"
}
```

**Response:**
```json
{
  "overall_score": 8.5,
  "principles": {
    "solid": {
      "score": 9.0,
      "violations": [],
      "suggestions": ["Consider extracting interface..."]
    },
    "soc": {
      "score": 8.0,
      "violations": ["Mixed business logic with presentation"],
      "suggestions": ["Separate data processing..."]
    }
  }
}
```

#### GET /api/v1/code/extensions
Get list of available code extensions.

#### POST /api/v1/code/extensions/install
Install a code extension.

#### POST /api/v1/code/format
Format code according to language standards.

### Monitoring

#### GET /api/v1/monitoring/system/status
Get current system status and metrics.

**Response:**
```json
{
  "cpu": {
    "usage_percent": 25.4,
    "cores": 8,
    "frequency": 2.8,
    "temperature": 45.2
  },
  "memory": {
    "total_gb": 16.0,
    "used_gb": 8.2,
    "usage_percent": 51.3,
    "available_gb": 7.8
  },
  "disk": {
    "total_gb": 512.0,
    "used_gb": 256.8,
    "usage_percent": 50.2,
    "free_gb": 255.2
  },
  "timestamp": "2025-09-14T23:07:00Z"
}
```

#### GET /api/v1/monitoring/system/history
Get historical system metrics.

**Query Parameters:**
- `hours`: Number of hours of history (default: 24)

#### GET /api/v1/monitoring/processes
Get list of running processes.

**Query Parameters:**
- `limit`: Maximum number of processes (default: 50)

#### GET /api/v1/monitoring/logs
Get system logs.

**Query Parameters:**
- `level`: Log level filter (DEBUG, INFO, WARNING, ERROR)
- `limit`: Maximum number of log entries (default: 100)

#### GET /api/v1/monitoring/alerts
Get active system alerts.

#### POST /api/v1/monitoring/alerts/{alert_id}/acknowledge
Acknowledge a system alert.

## WebSocket Endpoints

### Real-time Monitoring

#### WS /api/v1/monitoring/realtime
Real-time system monitoring data.

**Message Format:**
```json
{
  "timestamp": "2025-09-14T23:07:00Z",
  "cpu_usage": 25.4,
  "memory_usage": 51.3,
  "disk_usage": 50.2,
  "network_in": 1024,
  "network_out": 512,
  "active_processes": 156
}
```

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "detail": "Error description",
  "status_code": 400,
  "type": "ValidationError"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API endpoints are rate-limited to prevent abuse:

- **Authentication**: 5 requests per minute
- **General API**: 100 requests per minute
- **Code Execution**: 10 requests per minute
- **File Upload**: 5 requests per minute

## Examples

### Python Client Example

```python
import requests

# Login
response = requests.post("http://localhost:8000/api/v1/auth/login", json={
    "username": "your_username",
    "password": "your_password"
})
token = response.json()["access_token"]

# Use API with token
headers = {"Authorization": f"Bearer {token}"}
response = requests.get("http://localhost:8000/api/v1/automation/tasks", headers=headers)
tasks = response.json()
```

### JavaScript Client Example

```javascript
// Login
const loginResponse = await fetch("http://localhost:8000/api/v1/auth/login", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    username: "your_username",
    password: "your_password"
  })
});
const { access_token } = await loginResponse.json();

// Use API with token
const tasksResponse = await fetch("http://localhost:8000/api/v1/automation/tasks", {
  headers: { "Authorization": `Bearer ${access_token}` }
});
const tasks = await tasksResponse.json();
```

## SDK and Libraries

Official SDKs are available for:

- **Python**: `pip install lonibeta-sdk`
- **JavaScript/TypeScript**: `npm install lonibeta-sdk`
- **Go**: `go get github.com/lonibeta/go-sdk`

## Support

For API support and questions:

- **Documentation**: Check this API documentation
- **GitHub Issues**: Report bugs and feature requests
- **Community**: Join our Discord server
- **Email**: <EMAIL>
