"""
Task management for automation system.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import uuid
import logging

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(Enum):
    """Types of automation tasks."""
    FILE_ORGANIZATION = "file_organization"
    EMAIL_MANAGEMENT = "email_management"
    SYSTEM_CLEANUP = "system_cleanup"
    DATA_BACKUP = "data_backup"
    CUSTOM_SCRIPT = "custom_script"


@dataclass
class Task:
    """Automation task definition."""
    id: str
    name: str
    description: str
    task_type: TaskType
    schedule: str  # Cron expression
    config: Dict[str, Any]
    status: TaskStatus = TaskStatus.PENDING
    created_at: datetime = None
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    run_count: int = 0
    error_count: int = 0
    last_error: Optional[str] = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.id is None:
            self.id = str(uuid.uuid4())


class TaskManager:
    """Manages automation tasks lifecycle."""

    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}

    async def create_task(
        self,
        name: str,
        description: str,
        task_type: TaskType,
        schedule: str,
        config: Dict[str, Any]
    ) -> Task:
        """Create a new automation task."""
        task = Task(
            id=str(uuid.uuid4()),
            name=name,
            description=description,
            task_type=task_type,
            schedule=schedule,
            config=config
        )
        
        self.tasks[task.id] = task
        logger.info(f"Created task: {task.name} ({task.id})")
        
        return task

    async def get_task(self, task_id: str) -> Optional[Task]:
        """Get a task by ID."""
        return self.tasks.get(task_id)

    async def get_all_tasks(self) -> List[Task]:
        """Get all tasks."""
        return list(self.tasks.values())

    async def update_task(
        self,
        task_id: str,
        **updates
    ) -> Optional[Task]:
        """Update a task."""
        task = self.tasks.get(task_id)
        if not task:
            return None

        for key, value in updates.items():
            if hasattr(task, key):
                setattr(task, key, value)

        logger.info(f"Updated task: {task.name} ({task.id})")
        return task

    async def delete_task(self, task_id: str) -> bool:
        """Delete a task."""
        if task_id in self.running_tasks:
            await self.cancel_task(task_id)

        if task_id in self.tasks:
            task = self.tasks.pop(task_id)
            logger.info(f"Deleted task: {task.name} ({task.id})")
            return True

        return False

    async def start_task(self, task_id: str) -> bool:
        """Start a task execution."""
        task = self.tasks.get(task_id)
        if not task:
            return False

        if task_id in self.running_tasks:
            logger.warning(f"Task {task.name} is already running")
            return False

        task.status = TaskStatus.RUNNING
        
        # Create async task for execution
        async_task = asyncio.create_task(self._execute_task(task))
        self.running_tasks[task_id] = async_task

        logger.info(f"Started task: {task.name} ({task.id})")
        return True

    async def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task."""
        if task_id not in self.running_tasks:
            return False

        async_task = self.running_tasks.pop(task_id)
        async_task.cancel()

        task = self.tasks.get(task_id)
        if task:
            task.status = TaskStatus.CANCELLED

        logger.info(f"Cancelled task: {task_id}")
        return True

    async def _execute_task(self, task: Task) -> None:
        """Execute a task based on its type."""
        try:
            task.last_run = datetime.now()
            task.run_count += 1

            # Execute based on task type
            if task.task_type == TaskType.FILE_ORGANIZATION:
                await self._execute_file_organization(task)
            elif task.task_type == TaskType.EMAIL_MANAGEMENT:
                await self._execute_email_management(task)
            elif task.task_type == TaskType.SYSTEM_CLEANUP:
                await self._execute_system_cleanup(task)
            elif task.task_type == TaskType.DATA_BACKUP:
                await self._execute_data_backup(task)
            elif task.task_type == TaskType.CUSTOM_SCRIPT:
                await self._execute_custom_script(task)
            else:
                raise ValueError(f"Unknown task type: {task.task_type}")

            task.status = TaskStatus.COMPLETED
            logger.info(f"Task completed: {task.name} ({task.id})")

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error_count += 1
            task.last_error = str(e)
            logger.error(f"Task failed: {task.name} ({task.id}) - {e}")

        finally:
            # Remove from running tasks
            if task.id in self.running_tasks:
                self.running_tasks.pop(task.id)

    async def _execute_file_organization(self, task: Task) -> None:
        """Execute file organization task."""
        # TODO: Implement file organization logic
        await asyncio.sleep(2)  # Simulate work
        logger.info(f"Organized files for task: {task.name}")

    async def _execute_email_management(self, task: Task) -> None:
        """Execute email management task."""
        # TODO: Implement email management logic
        await asyncio.sleep(3)  # Simulate work
        logger.info(f"Managed emails for task: {task.name}")

    async def _execute_system_cleanup(self, task: Task) -> None:
        """Execute system cleanup task."""
        # TODO: Implement system cleanup logic
        await asyncio.sleep(1)  # Simulate work
        logger.info(f"Cleaned system for task: {task.name}")

    async def _execute_data_backup(self, task: Task) -> None:
        """Execute data backup task."""
        # TODO: Implement data backup logic
        await asyncio.sleep(5)  # Simulate work
        logger.info(f"Backed up data for task: {task.name}")

    async def _execute_custom_script(self, task: Task) -> None:
        """Execute custom script task."""
        # TODO: Implement custom script execution
        await asyncio.sleep(2)  # Simulate work
        logger.info(f"Executed custom script for task: {task.name}")

    def get_task_stats(self) -> Dict[str, Any]:
        """Get task statistics."""
        total_tasks = len(self.tasks)
        running_tasks = len(self.running_tasks)
        completed_tasks = sum(1 for t in self.tasks.values() if t.status == TaskStatus.COMPLETED)
        failed_tasks = sum(1 for t in self.tasks.values() if t.status == TaskStatus.FAILED)

        return {
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
            "completed_tasks": completed_tasks,
            "failed_tasks": failed_tasks,
            "success_rate": completed_tasks / total_tasks if total_tasks > 0 else 0
        }
