{".class": "MypyFile", "_fullname": "lonibeta.core.ai.hardware_compatibility", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CompatibilityAnalysis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis", "name": "CompatibilityAnalysis", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "model_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "compatibility_rating", "type": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 58, "name": "can_run", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 59, "name": "memory_requirements", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 60, "name": "performance_estimates", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "recommendations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "warnings", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "alternative_models", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "optimal_quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "estimated_download_time_minutes", "type": "builtins.float"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model_id", "compatibility_rating", "can_run", "memory_requirements", "performance_estimates", "recommendations", "warnings", "alternative_models", "optimal_quantization", "estimated_download_time_minutes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "model_id", "compatibility_rating", "can_run", "memory_requirements", "performance_estimates", "recommendations", "warnings", "alternative_models", "optimal_quantization", "estimated_download_time_minutes"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis", "builtins.str", "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "builtins.bool", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CompatibilityAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "model_id"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "compatibility_rating"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "can_run"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "memory_requirements"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "performance_estimates"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "recommendations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "warnings"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "alternative_models"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "optimal_quantization"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "estimated_download_time_minutes"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model_id", "compatibility_rating", "can_run", "memory_requirements", "performance_estimates", "recommendations", "warnings", "alternative_models", "optimal_quantization", "estimated_download_time_minutes"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model_id", "compatibility_rating", "can_run", "memory_requirements", "performance_estimates", "recommendations", "warnings", "alternative_models", "optimal_quantization", "estimated_download_time_minutes"], "arg_types": ["builtins.str", "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "builtins.bool", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CompatibilityAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["model_id", "compatibility_rating", "can_run", "memory_requirements", "performance_estimates", "recommendations", "warnings", "alternative_models", "optimal_quantization", "estimated_download_time_minutes"], "arg_types": ["builtins.str", "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "builtins.bool", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "lonibeta.core.ai.model_discovery.QuantizationType", "builtins.float"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CompatibilityAnalysis", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "alternative_models": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.alternative_models", "name": "alternative_models", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "can_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.can_run", "name": "can_run", "type": "builtins.bool"}}, "compatibility_rating": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.compatibility_rating", "name": "compatibility_rating", "type": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating"}}, "estimated_download_time_minutes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.estimated_download_time_minutes", "name": "estimated_download_time_minutes", "type": "builtins.float"}}, "memory_requirements": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.memory_requirements", "name": "memory_requirements", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "model_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.model_id", "name": "model_id", "type": "builtins.str"}}, "optimal_quantization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.optimal_quantization", "name": "optimal_quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}}, "performance_estimates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.performance_estimates", "name": "performance_estimates", "type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "recommendations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.recommendations", "name": "recommendations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "warnings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.warnings", "name": "warnings", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompatibilityRating": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "name": "CompatibilityRating", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "EXCELLENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.EXCELLENT", "name": "EXCELLENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "excellent"}, "type_ref": "builtins.str"}}}, "FAIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.FAIR", "name": "FAIR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "fair"}, "type_ref": "builtins.str"}}}, "GOOD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.GOOD", "name": "GOOD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "good"}, "type_ref": "builtins.str"}}}, "INCOMPATIBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.INCOMPATIBLE", "name": "INCOMPATIBLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "incompatible"}, "type_ref": "builtins.str"}}}, "POOR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.POOR", "name": "POOR", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "poor"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ComputeCapability": {".class": "SymbolTableNode", "cross_ref": "lonibeta.core.monitoring.system_monitor.ComputeCapability", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "GPUInfo": {".class": "SymbolTableNode", "cross_ref": "lonibeta.core.monitoring.system_monitor.GPUInfo", "kind": "Gdef"}, "HardwareCompatibilityEngine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "name": "HardwareCompatibilityEngine", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.__init__", "name": "__init__", "type": null}}, "_calculate_cpu_performance_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._calculate_cpu_performance_factor", "name": "_calculate_cpu_performance_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cpu_performance_factor of HardwareCompatibilityEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_gpu_performance_factor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._calculate_gpu_performance_factor", "name": "_calculate_gpu_performance_factor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_gpu_performance_factor of HardwareCompatibilityEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_memory_requirements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._calculate_memory_requirements", "name": "_calculate_memory_requirements", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "model"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_memory_requirements of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_can_use_gpu": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "hardware", "mem_req"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._can_use_gpu", "name": "_can_use_gpu", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "hardware", "mem_req"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_can_use_gpu of HardwareCompatibilityEngine", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_classify_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tokens_per_sec"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._classify_performance", "name": "_classify_performance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tokens_per_sec"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_classify_performance of HardwareCompatibilityEngine", "ret_type": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_compatibility": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements", "performance_estimates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._determine_compatibility", "name": "_determine_compatibility", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements", "performance_estimates"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_compatibility of HardwareCompatibilityEngine", "ret_type": {".class": "TupleType", "implicit": false, "items": ["lonibeta.core.ai.hardware_compatibility.CompatibilityRating", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_estimate_download_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "internet_speed_mbps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._estimate_download_time", "name": "_estimate_download_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "internet_speed_mbps"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_estimate_download_time of HardwareCompatibilityEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_estimate_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._estimate_performance", "name": "_estimate_performance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_estimate_performance of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_find_optimal_quantization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "hardware", "memory_requirements", "performance_estimates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._find_optimal_quantization", "name": "_find_optimal_quantization", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "hardware", "memory_requirements", "performance_estimates"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_find_optimal_quantization of HardwareCompatibilityEngine", "ret_type": "lonibeta.core.ai.model_discovery.QuantizationType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "performance_estimates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._generate_recommendations", "name": "_generate_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "performance_estimates"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_recommendations of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_warnings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._generate_warnings", "name": "_generate_warnings", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "memory_requirements"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", {".class": "Instance", "args": ["lonibeta.core.ai.model_discovery.QuantizationType", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_warnings of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_total_vram": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._get_total_vram", "name": "_get_total_vram", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hardware"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_total_vram of HardwareCompatibilityEngine", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_bottleneck": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "hardware", "mem_req", "can_use_gpu"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._identify_bottleneck", "name": "_identify_bottleneck", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "hardware", "mem_req", "can_use_gpu"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", "lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_bottleneck of HardwareCompatibilityEngine", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_suggest_alternatives": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "compatibility"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine._suggest_alternatives", "name": "_suggest_alternatives", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "model", "hardware", "compatibility"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", "lonibeta.core.ai.hardware_compatibility.CompatibilityRating"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_suggest_alternatives of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_compatibility": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "hardware", "internet_speed_mbps"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.analyze_compatibility", "name": "analyze_compatibility", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "model", "hardware", "internet_speed_mbps"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "lonibeta.core.ai.model_discovery.ModelInfo", "lonibeta.core.monitoring.system_monitor.HardwareSpecs", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_compatibility of HardwareCompatibilityEngine", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "lonibeta.core.ai.hardware_compatibility.CompatibilityAnalysis"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "memory_multipliers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.memory_multipliers", "name": "memory_multipliers", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quantization_factors": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.quantization_factors", "name": "quantization_factors", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quantization_performance": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.quantization_performance", "name": "quantization_performance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.HardwareCompatibilityEngine", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HardwareSpecs": {".class": "SymbolTableNode", "cross_ref": "lonibeta.core.monitoring.system_monitor.HardwareSpecs", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MemoryRequirement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "name": "MemoryRequirement", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "minimum_ram_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 36, "name": "recommended_ram_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 37, "name": "minimum_vram_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 38, "name": "recommended_vram_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 39, "name": "quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "minimum_ram_gb", "recommended_ram_gb", "minimum_vram_gb", "recommended_vram_gb", "quantization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "minimum_ram_gb", "recommended_ram_gb", "minimum_vram_gb", "recommended_vram_gb", "quantization"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.model_discovery.QuantizationType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryRequirement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "minimum_ram_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "recommended_ram_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "minimum_vram_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "recommended_vram_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quantization"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["minimum_ram_gb", "recommended_ram_gb", "minimum_vram_gb", "recommended_vram_gb", "quantization"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["minimum_ram_gb", "recommended_ram_gb", "minimum_vram_gb", "recommended_vram_gb", "quantization"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.model_discovery.QuantizationType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryRequirement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["minimum_ram_gb", "recommended_ram_gb", "minimum_vram_gb", "recommended_vram_gb", "quantization"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.model_discovery.QuantizationType"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of MemoryRequirement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "minimum_ram_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.minimum_ram_gb", "name": "minimum_ram_gb", "type": "builtins.float"}}, "minimum_vram_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.minimum_vram_gb", "name": "minimum_vram_gb", "type": "builtins.float"}}, "quantization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.quantization", "name": "quantization", "type": "lonibeta.core.ai.model_discovery.QuantizationType"}}, "recommended_ram_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.recommended_ram_gb", "name": "recommended_ram_gb", "type": "builtins.float"}}, "recommended_vram_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.recommended_vram_gb", "name": "recommended_vram_gb", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.MemoryRequirement", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ModelInfo": {".class": "SymbolTableNode", "cross_ref": "lonibeta.core.ai.model_discovery.ModelInfo", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PerformanceEstimate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate", "name": "PerformanceEstimate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "tokens_per_second", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 46, "name": "inference_latency_ms", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "memory_usage_gb", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "gpu_utilization_percent", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "performance_level", "type": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "bottleneck", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.PerformanceEstimate", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "tokens_per_second", "inference_latency_ms", "memory_usage_gb", "gpu_utilization_percent", "performance_level", "bottleneck"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "tokens_per_second", "inference_latency_ms", "memory_usage_gb", "gpu_utilization_percent", "performance_level", "bottleneck"], "arg_types": ["lonibeta.core.ai.hardware_compatibility.PerformanceEstimate", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PerformanceEstimate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "tokens_per_second"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "inference_latency_ms"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "memory_usage_gb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gpu_utilization_percent"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "performance_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bottleneck"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["tokens_per_second", "inference_latency_ms", "memory_usage_gb", "gpu_utilization_percent", "performance_level", "bottleneck"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["tokens_per_second", "inference_latency_ms", "memory_usage_gb", "gpu_utilization_percent", "performance_level", "bottleneck"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PerformanceEstimate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["tokens_per_second", "inference_latency_ms", "memory_usage_gb", "gpu_utilization_percent", "performance_level", "bottleneck"], "arg_types": ["builtins.float", "builtins.float", "builtins.float", "builtins.float", "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of PerformanceEstimate", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bottleneck": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.bottleneck", "name": "bottleneck", "type": "builtins.str"}}, "gpu_utilization_percent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.gpu_utilization_percent", "name": "gpu_utilization_percent", "type": "builtins.float"}}, "inference_latency_ms": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.inference_latency_ms", "name": "inference_latency_ms", "type": "builtins.float"}}, "memory_usage_gb": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.memory_usage_gb", "name": "memory_usage_gb", "type": "builtins.float"}}, "performance_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.performance_level", "name": "performance_level", "type": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel"}}, "tokens_per_second": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.tokens_per_second", "name": "tokens_per_second", "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.PerformanceEstimate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PerformanceLevel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "name": "PerformanceLevel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "lonibeta.core.ai.hardware_compatibility", "mro": ["lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "FAST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.FAST", "name": "FAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "fast"}, "type_ref": "builtins.str"}}}, "MODERATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.MODERATE", "name": "MODERATE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "moderate"}, "type_ref": "builtins.str"}}}, "SLOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.SLOW", "name": "SLOW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "slow"}, "type_ref": "builtins.str"}}}, "VERY_FAST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.VERY_FAST", "name": "VERY_FAST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "very_fast"}, "type_ref": "builtins.str"}}}, "VERY_SLOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.VERY_SLOW", "name": "VERY_SLOW", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "very_slow"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "lonibeta.core.ai.hardware_compatibility.PerformanceLevel", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QuantizationType": {".class": "SymbolTableNode", "cross_ref": "lonibeta.core.ai.model_discovery.QuantizationType", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "lonibeta.core.ai.hardware_compatibility.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}}, "path": "E:\\usb\\Projects\\LoniBeta\\backend\\lonibeta\\core\\ai\\hardware_compatibility.py"}