# Build LoniBeta for production

Write-Host "🏗️  Building LoniBeta for Production..." -ForegroundColor Green

# Build frontend
Write-Host "⚛️  Building frontend..." -ForegroundColor Yellow
Set-Location frontend

Write-Host "Running production build..." -ForegroundColor Cyan
bun run build

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Frontend build successful" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend build failed" -ForegroundColor Red
    exit 1
}

Set-Location ..

# Build backend (create distribution)
Write-Host "🐍 Preparing backend..." -ForegroundColor Yellow
Set-Location backend

Write-Host "Installing production dependencies..." -ForegroundColor Cyan
uv sync --no-dev

Write-Host "Running code quality checks..." -ForegroundColor Cyan
uv run black lonibeta --check
uv run isort lonibeta --check-only
uv run flake8 lonibeta
uv run mypy lonibeta

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Backend code quality checks passed" -ForegroundColor Green
} else {
    Write-Host "❌ Backend code quality checks failed" -ForegroundColor Red
    exit 1
}

Set-Location ..

# Create build directory
Write-Host "📦 Creating build package..." -ForegroundColor Yellow
$buildDir = "build"
if (Test-Path $buildDir) {
    Remove-Item $buildDir -Recurse -Force
}
New-Item -ItemType Directory -Path $buildDir -Force | Out-Null

# Copy backend
Write-Host "Copying backend files..." -ForegroundColor Cyan
Copy-Item "backend" "$buildDir/backend" -Recurse -Exclude @("__pycache__", "*.pyc", ".pytest_cache", "htmlcov", ".coverage")

# Copy frontend build
Write-Host "Copying frontend build..." -ForegroundColor Cyan
Copy-Item "frontend/dist" "$buildDir/frontend" -Recurse

# Copy scripts and docs
Write-Host "Copying scripts and documentation..." -ForegroundColor Cyan
Copy-Item "scripts" "$buildDir/scripts" -Recurse
Copy-Item "README.md" "$buildDir/"
Copy-Item "docs" "$buildDir/docs" -Recurse -ErrorAction SilentlyContinue

# Create production start script
Write-Host "Creating production start script..." -ForegroundColor Cyan
@"
# LoniBeta Production Start Script

Write-Host "🚀 Starting LoniBeta Production Server..." -ForegroundColor Green

Set-Location backend
uv run uvicorn lonibeta.main:app --host 0.0.0.0 --port 8000
"@ | Out-File -FilePath "$buildDir/start-production.ps1" -Encoding UTF8

Write-Host "🎉 Production build complete!" -ForegroundColor Green
Write-Host "Build location: $buildDir" -ForegroundColor Cyan
Write-Host ""
Write-Host "To deploy:" -ForegroundColor Yellow
Write-Host "1. Copy the build directory to your production server" -ForegroundColor Cyan
Write-Host "2. Install uv and Python on the production server" -ForegroundColor Cyan
Write-Host "3. Run: .\start-production.ps1" -ForegroundColor Cyan
